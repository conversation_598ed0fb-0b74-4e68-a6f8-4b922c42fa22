<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Privacy Policy for v.calculator.city - Learn how we collect, use, and protect your personal information when using our free online calculators.">
    <meta name="keywords" content="privacy policy, data protection, calculator privacy, v.calculator.city privacy">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="Privacy Policy | v.calculator.city">
    <meta property="og:description" content="Privacy Policy for v.calculator.city - Learn how we collect, use, and protect your personal information when using our free online calculators.">
    <meta property="og:site_name" content="v.calculator.city">
    <meta property="og:url" content="https://v.calculator.city/privacy-policy">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://v.calculator.city/privacy-policy">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <title>Privacy Policy | v.calculator.city</title>
    
    <style>
        /* General Body and HTML styles */
        html { -webkit-font-smoothing: antialiased; box-sizing: border-box; text-size-adjust: 100%; scroll-padding-top: 119px; }
        *, ::before, ::after { box-sizing: inherit; }
        body { margin: 0px; color: rgb(10, 10, 10); font-family: Verdana, sans-serif; font-size: 15px; font-weight: 400; line-height: 141%; letter-spacing: 0px; background-color: rgb(247, 249, 255); background-position: center 0px; background-repeat: no-repeat; overflow-y: scroll; }
        strong, b { font-weight: 700; }

        /* Header Styles */
        .app-header { z-index: 1202; top: 0px; background: rgb(255, 255, 255); width: 100%; box-shadow: rgba(59, 104, 252, 0.1) 0px 6px 16px 0px, rgba(41, 76, 194, 0.06) 0px 1px 2.8px 1px; padding: 8px 16px; position: fixed; left: 0px; right: 0px; }
        .header-container { max-width: 1920px; margin-inline: auto; display: flex; align-items: center; width: 100%; }
        .logo-section { position: relative; flex-grow: 1; display: flex; align-items: center; justify-content: space-between; padding-right: 16px; }
        .logo-link { text-decoration: none; display: flex; align-items: center; min-height: 40px; border-radius: 8px; color: #3B68FC; font-size: 24px; font-weight: bold; }
        .search-section { display: flex; align-items: center; flex-grow: 1; padding-left: 0; }
        .nav-section { align-items: center; width: 100%; display: none; }
        .nav-list { display: flex; flex-direction: column; gap: 16px; list-style-type: none; margin: 0; padding: 0; }
        .nav-link { text-decoration: none; color: rgb(33, 36, 39); }
        .nav-link-box { padding: 0px; position: relative; color: rgb(33, 36, 39); }
        .nav-link-text { padding: 0px; margin: 0px; color: inherit; text-align: left; font-size: 13.4px; font-style: normal; font-weight: 500; line-height: 20px; }

        /* Main Content Styles */
        .main-container { box-sizing: border-box; margin-inline: auto; padding-bottom: 40px; padding-top: 130px; max-width: 800px; padding-left: 20px; padding-right: 20px; }
        .content-section { background: white; border-radius: 12px; padding: 40px; margin-bottom: 40px; }
        .page-title { font-size: 36px; font-weight: 700; color: rgb(33, 36, 39); margin-bottom: 20px; text-align: center; }
        .last-updated { text-align: center; color: rgb(75, 82, 89); margin-bottom: 40px; font-style: italic; }
        .section-title { font-size: 24px; font-weight: 600; color: rgb(33, 36, 39); margin-top: 30px; margin-bottom: 15px; }
        .subsection-title { font-size: 18px; font-weight: 600; color: rgb(33, 36, 39); margin-top: 25px; margin-bottom: 10px; }
        .content-text { color: rgb(75, 82, 89); line-height: 1.6; margin-bottom: 15px; }
        .content-list { color: rgb(75, 82, 89); line-height: 1.6; margin-bottom: 15px; padding-left: 20px; }
        .content-list li { margin-bottom: 8px; }
        .contact-info { background: rgb(247, 249, 255); padding: 20px; border-radius: 8px; margin-top: 30px; }
        .contact-title { font-size: 18px; font-weight: 600; color: rgb(33, 36, 39); margin-bottom: 10px; }
        .contact-link { color: #3B68FC; text-decoration: none; }
        .contact-link:hover { color: #294CC2; text-decoration: underline; }

        /* Footer Styles */
        .footer { background: rgb(255, 255, 255); padding-bottom: 0px; margin-top: 60px; }
        .footer-container { margin-inline: auto; max-width: 1920px; }
        .footer-base { padding: 24px; display: flex; gap: 24px; align-items: center; justify-content: center; flex-direction: column; border-top: 1px solid rgb(223, 226, 235); }
        .footer-copyright { padding: 0px; margin: 0px; color: rgb(116, 128, 145); text-align: left; font-size: 13.4px; font-style: normal; font-weight: 600; line-height: 24px; }
        .footer-base-link { color: rgb(116, 128, 145); transition: color 200ms linear; text-decoration: none; font-size: 13.4px; font-weight: 600; line-height: 24px; }
        .footer-base-link:hover { color: rgb(59, 104, 252); }

        /* Responsive Media Queries */
        @media (min-width: 584px) {
            .app-header { padding: 16px 24px; }
            .logo-section { flex-grow: 0; padding-right: 0; }
            .search-section { padding-left: 16px; }
            .main-container { padding-top: 58px; }
            .footer-base { flex-direction: row; }
        }
        @media (min-width: 1146px) {
            .main-container { padding-top: 113px; }
            .header-container { flex-direction: column; }
            .nav-section { display: block; padding-top: 16px; }
            .nav-list { flex-direction: row; justify-content: space-between; gap: 0; }
            .nav-link-box::after { display: block; position: absolute; content: ""; inset: -8px 0 -16px; border-bottom: 3px solid transparent; }
            .nav-link-box:hover { color: rgb(59, 104, 252); }
        }
    </style>
</head>
<body>
    <div id="__next">
        <header class="app-header">
            <div class="header-container">
                <div class="header-top-row" style="display: flex; align-items: center; width: 100%;">
                    <div class="logo-section">
                        <a class="logo-link" href="https://v.calculator.city">
                            v.calculator.city
                        </a>
                    </div>
                    <div class="search-section">
                        <!-- Search bar can be implemented here -->
                    </div>
                </div>
                <nav class="nav-section">
                    <ul class="nav-list">
                        <li><a class="nav-link" href="/biology-calculators"><div class="nav-link-box"><span class="nav-link-text">Biology</span></div></a></li>
                        <li><a class="nav-link" href="/chemistry-calculators"><div class="nav-link-box"><span class="nav-link-text">Chemistry</span></div></a></li>
                        <li><a class="nav-link" href="/math-calculators"><div class="nav-link-box"><span class="nav-link-text">Math</span></div></a></li>
                        <li><a class="nav-link" href="/physics-calculators"><div class="nav-link-box"><span class="nav-link-text">Physics</span></div></a></li>
                        <li><a class="nav-link" href="/health-calculators"><div class="nav-link-box"><span class="nav-link-text">Health</span></div></a></li>
                        <li><a class="nav-link" href="/finance-calculators"><div class="nav-link-box"><span class="nav-link-text">Finance</span></div></a></li>
                    </ul>
                </nav>
            </div>
        </header>

        <main class="main-container">
            <div class="content-section">
                <h1 class="page-title">Privacy Policy</h1>
                <p class="last-updated">Last updated: December 19, 2024</p>

                <h2 class="section-title">1. Introduction</h2>
                <p class="content-text">
                    Welcome to v.calculator.city ("we," "our," or "us"). We are committed to protecting your privacy and ensuring the security of your personal information. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you visit our website and use our free online calculators.
                </p>

                <h2 class="section-title">2. Information We Collect</h2>
                
                <h3 class="subsection-title">2.1 Information You Provide</h3>
                <p class="content-text">
                    We may collect information that you voluntarily provide to us, including:
                </p>
                <ul class="content-list">
                    <li>Contact information when you reach out to us</li>
                    <li>Feedback and suggestions you submit</li>
                    <li>Calculator inputs and preferences (stored locally on your device)</li>
                </ul>

                <h3 class="subsection-title">2.2 Automatically Collected Information</h3>
                <p class="content-text">
                    When you visit our website, we may automatically collect certain information, including:
                </p>
                <ul class="content-list">
                    <li>IP address and location information</li>
                    <li>Browser type and version</li>
                    <li>Device information and operating system</li>
                    <li>Pages visited and time spent on our site</li>
                    <li>Referring website information</li>
                </ul>

                <h2 class="section-title">3. How We Use Your Information</h2>
                <p class="content-text">
                    We use the collected information for the following purposes:
                </p>
                <ul class="content-list">
                    <li>To provide and maintain our calculator services</li>
                    <li>To improve our website functionality and user experience</li>
                    <li>To analyze usage patterns and optimize our services</li>
                    <li>To respond to your inquiries and provide customer support</li>
                    <li>To detect and prevent fraud or abuse</li>
                    <li>To comply with legal obligations</li>
                </ul>

                <h2 class="section-title">4. Information Sharing and Disclosure</h2>
                <p class="content-text">
                    We do not sell, trade, or otherwise transfer your personal information to third parties, except in the following circumstances:
                </p>
                <ul class="content-list">
                    <li>With your explicit consent</li>
                    <li>To comply with legal requirements or court orders</li>
                    <li>To protect our rights, property, or safety, or that of our users</li>
                    <li>In connection with a business transfer or merger</li>
                    <li>With trusted service providers who assist us in operating our website (under strict confidentiality agreements)</li>
                </ul>

                <h2 class="section-title">5. Data Security</h2>
                <p class="content-text">
                    We implement appropriate technical and organizational security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. However, no method of transmission over the internet or electronic storage is 100% secure.
                </p>

                <h2 class="section-title">6. Cookies and Tracking Technologies</h2>
                <p class="content-text">
                    We use cookies and similar tracking technologies to enhance your browsing experience. You can control cookie settings through your browser preferences. Disabling cookies may affect some functionality of our website.
                </p>

                <h2 class="section-title">7. Your Rights and Choices</h2>
                <p class="content-text">
                    Depending on your location, you may have the following rights regarding your personal information:
                </p>
                <ul class="content-list">
                    <li>Access to your personal information</li>
                    <li>Correction of inaccurate information</li>
                    <li>Deletion of your personal information</li>
                    <li>Restriction of processing</li>
                    <li>Data portability</li>
                    <li>Objection to processing</li>
                </ul>

                <h2 class="section-title">8. Children's Privacy</h2>
                <p class="content-text">
                    Our services are not directed to children under the age of 13. We do not knowingly collect personal information from children under 13. If we become aware that we have collected personal information from a child under 13, we will take steps to delete such information.
                </p>

                <h2 class="section-title">9. International Data Transfers</h2>
                <p class="content-text">
                    Your information may be transferred to and processed in countries other than your own. We ensure appropriate safeguards are in place to protect your information in accordance with this Privacy Policy.
                </p>

                <h2 class="section-title">10. Changes to This Privacy Policy</h2>
                <p class="content-text">
                    We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the "Last updated" date. We encourage you to review this Privacy Policy periodically.
                </p>

                <div class="contact-info">
                    <h3 class="contact-title">Contact Us</h3>
                    <p class="content-text">
                        If you have any questions about this Privacy Policy or our privacy practices, please contact us at:
                    </p>
                    <p class="content-text">
                        Email: <a href="mailto:<EMAIL>" class="contact-link"><EMAIL></a><br>
                        Website: <a href="/contact" class="contact-link">v.calculator.city/contact</a>
                    </p>
                </div>
            </div>
        </main>

        <footer class="footer">
            <div class="footer-container">
                <div class="footer-base">
                    <span class="footer-copyright">© 2024 v.calculator.city. All rights reserved.</span>
                    <div style="display: flex; gap: 16px;">
                        <a href="/privacy-policy" class="footer-base-link">Privacy Policy</a>
                        <a href="/terms-of-service" class="footer-base-link">Terms of Service</a>
                        <a href="/contact" class="footer-base-link">Contact</a>
                    </div>
                </div>
            </div>
        </footer>
    </div>
</body>
</html>
