<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="About v.calculator.city - Learn about our mission to provide free, accurate, and easy-to-use online calculators for education, research, and everyday use.">
    <meta name="keywords" content="about v.calculator.city, calculator website, free online calculators, educational tools, scientific calculators">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="About Us | v.calculator.city">
    <meta property="og:description" content="About v.calculator.city - Learn about our mission to provide free, accurate, and easy-to-use online calculators for education, research, and everyday use.">
    <meta property="og:site_name" content="v.calculator.city">
    <meta property="og:url" content="https://v.calculator.city/about">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://v.calculator.city/about">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <title>About Us | v.calculator.city</title>

    <!-- Google AdSense -->
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2205593928173688"
         crossorigin="anonymous"></script>

    <style>
        /* General Body and HTML styles */
        html { -webkit-font-smoothing: antialiased; box-sizing: border-box; text-size-adjust: 100%; scroll-padding-top: 119px; }
        *, ::before, ::after { box-sizing: inherit; }
        body { margin: 0px; color: rgb(10, 10, 10); font-family: Verdana, sans-serif; font-size: 15px; font-weight: 400; line-height: 141%; letter-spacing: 0px; background-color: rgb(247, 249, 255); background-position: center 0px; background-repeat: no-repeat; overflow-y: scroll; }
        strong, b { font-weight: 700; }

        /* Header Styles */
        .app-header { z-index: 1202; top: 0px; background: rgb(255, 255, 255); width: 100%; box-shadow: rgba(59, 104, 252, 0.1) 0px 6px 16px 0px, rgba(41, 76, 194, 0.06) 0px 1px 2.8px 1px; padding: 8px 16px; position: fixed; left: 0px; right: 0px; }
        .header-container { max-width: 1920px; margin-inline: auto; display: flex; align-items: center; width: 100%; }
        .logo-section { position: relative; flex-grow: 1; display: flex; align-items: center; justify-content: space-between; padding-right: 16px; }
        .logo-link { text-decoration: none; display: flex; align-items: center; min-height: 40px; border-radius: 8px; color: #3B68FC; font-size: 24px; font-weight: bold; }
        .search-section { display: flex; align-items: center; flex-grow: 1; padding-left: 0; }
        .nav-section { align-items: center; width: 100%; display: none; }
        .nav-list { display: flex; flex-direction: column; gap: 16px; list-style-type: none; margin: 0; padding: 0; }
        .nav-link { text-decoration: none; color: rgb(33, 36, 39); }
        .nav-link-box { padding: 0px; position: relative; color: rgb(33, 36, 39); }
        .nav-link-text { padding: 0px; margin: 0px; color: inherit; text-align: left; font-size: 13.4px; font-style: normal; font-weight: 500; line-height: 20px; }

        /* Main Content Styles */
        .main-container { box-sizing: border-box; margin-inline: auto; padding-bottom: 40px; padding-top: 130px; max-width: 800px; padding-left: 20px; padding-right: 20px; }
        .content-section { background: white; border-radius: 12px; padding: 40px; margin-bottom: 40px; }
        .page-title { font-size: 36px; font-weight: 700; color: rgb(33, 36, 39); margin-bottom: 20px; text-align: center; }
        .page-subtitle { text-align: center; color: rgb(75, 82, 89); margin-bottom: 40px; font-size: 18px; line-height: 1.6; }
        .section-title { font-size: 28px; font-weight: 600; color: rgb(33, 36, 39); margin-top: 40px; margin-bottom: 20px; }
        .subsection-title { font-size: 22px; font-weight: 600; color: rgb(33, 36, 39); margin-top: 30px; margin-bottom: 15px; }
        .content-text { color: rgb(75, 82, 89); line-height: 1.6; margin-bottom: 20px; }
        .highlight-box { background: rgb(247, 249, 255); padding: 30px; border-radius: 12px; margin: 30px 0; border-left: 4px solid #3B68FC; }
        .highlight-text { color: rgb(33, 36, 39); font-size: 18px; font-weight: 500; margin: 0; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 30px; margin: 40px 0; }
        .stat-card { text-align: center; background: rgb(247, 249, 255); padding: 30px; border-radius: 12px; }
        .stat-number { font-size: 36px; font-weight: 700; color: #3B68FC; margin-bottom: 10px; }
        .stat-label { color: rgb(75, 82, 89); font-weight: 500; }
        .values-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 30px; margin: 40px 0; }
        .value-card { background: white; border: 2px solid rgb(210, 220, 255); padding: 30px; border-radius: 12px; text-align: center; }
        .value-icon { font-size: 48px; margin-bottom: 20px; }
        .value-title { font-size: 20px; font-weight: 600; color: rgb(33, 36, 39); margin-bottom: 15px; }
        .value-description { color: rgb(75, 82, 89); line-height: 1.6; }
        .team-section { margin-top: 40px; }
        .team-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 30px; margin-top: 30px; }
        .team-card { background: white; border: 1px solid rgb(210, 220, 255); padding: 30px; border-radius: 12px; text-align: center; }
        .team-avatar { width: 80px; height: 80px; border-radius: 50%; background: #3B68FC; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; color: white; font-size: 32px; font-weight: 700; }
        .team-name { font-size: 18px; font-weight: 600; color: rgb(33, 36, 39); margin-bottom: 10px; }
        .team-role { color: rgb(75, 82, 89); margin-bottom: 15px; }
        .team-description { color: rgb(75, 82, 89); line-height: 1.6; font-size: 14px; }

        /* Footer Styles */
        .footer { background: rgb(255, 255, 255); padding-bottom: 0px; margin-top: 60px; }
        .footer-container { margin-inline: auto; max-width: 1920px; }
        .footer-base { padding: 24px; display: flex; gap: 24px; align-items: center; justify-content: center; flex-direction: column; border-top: 1px solid rgb(223, 226, 235); }
        .footer-copyright { padding: 0px; margin: 0px; color: rgb(116, 128, 145); text-align: left; font-size: 13.4px; font-style: normal; font-weight: 600; line-height: 24px; }
        .footer-base-link { color: rgb(116, 128, 145); transition: color 200ms linear; text-decoration: none; font-size: 13.4px; font-weight: 600; line-height: 24px; }
        .footer-base-link:hover { color: rgb(59, 104, 252); }

        /* Responsive Media Queries */
        @media (min-width: 584px) {
            .app-header { padding: 16px 24px; }
            .logo-section { flex-grow: 0; padding-right: 0; }
            .search-section { padding-left: 16px; }
            .main-container { padding-top: 58px; }
            .footer-base { flex-direction: row; }
        }
        @media (min-width: 1146px) {
            .main-container { padding-top: 113px; }
            .header-container { flex-direction: column; }
            .nav-section { display: block; padding-top: 16px; }
            .nav-list { flex-direction: row; justify-content: space-between; gap: 0; }
            .nav-link-box::after { display: block; position: absolute; content: ""; inset: -8px 0 -16px; border-bottom: 3px solid transparent; }
            .nav-link-box:hover { color: rgb(59, 104, 252); }
        }
    </style>
</head>
<body>
    <div id="__next">
        <header class="app-header">
            <div class="header-container">
                <div class="header-top-row" style="display: flex; align-items: center; width: 100%;">
                    <div class="logo-section">
                        <a class="logo-link" href="https://v.calculator.city">
                            v.calculator.city
                        </a>
                    </div>
                    <div class="search-section">
                        <!-- Search bar can be implemented here -->
                    </div>
                </div>
                <nav class="nav-section">
                    <ul class="nav-list">
                        <li><a class="nav-link" href="/biology-calculators"><div class="nav-link-box"><span class="nav-link-text">Biology</span></div></a></li>
                        <li><a class="nav-link" href="/chemistry-calculators"><div class="nav-link-box"><span class="nav-link-text">Chemistry</span></div></a></li>
                        <li><a class="nav-link" href="/math-calculators"><div class="nav-link-box"><span class="nav-link-text">Math</span></div></a></li>
                        <li><a class="nav-link" href="/physics-calculators"><div class="nav-link-box"><span class="nav-link-text">Physics</span></div></a></li>
                        <li><a class="nav-link" href="/health-calculators"><div class="nav-link-box"><span class="nav-link-text">Health</span></div></a></li>
                        <li><a class="nav-link" href="/finance-calculators"><div class="nav-link-box"><span class="nav-link-text">Finance</span></div></a></li>
                    </ul>
                </nav>
            </div>
        </header>

        <main class="main-container">
            <div class="content-section">
                <h1 class="page-title">About v.calculator.city</h1>
                <p class="page-subtitle">We're on a mission to make complex calculations simple, accessible, and free for everyone. From students to professionals, our calculators help millions solve problems every day.</p>

                <div class="highlight-box">
                    <p class="highlight-text">"We believe that powerful calculation tools should be free, accurate, and accessible to everyone, everywhere."</p>
                </div>

                <h2 class="section-title">Our Story</h2>
                <p class="content-text">
                    v.calculator.city was born from a simple observation: while the internet is full of information, finding reliable, easy-to-use calculation tools was surprisingly difficult. Students struggled with complex formulas, researchers needed quick verification of their work, and professionals required accurate calculations for critical decisions.
                </p>
                <p class="content-text">
                    Founded in 2024, we set out to create the most comprehensive collection of free online calculators, covering everything from basic arithmetic to advanced scientific computations. Our team of mathematicians, scientists, and developers work tirelessly to ensure every calculator is accurate, user-friendly, and based on the latest scientific standards.
                </p>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">100+</div>
                        <div class="stat-label">Free Calculators</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">1M+</div>
                        <div class="stat-label">Monthly Users</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">50M+</div>
                        <div class="stat-label">Calculations Performed</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">99.9%</div>
                        <div class="stat-label">Accuracy Rate</div>
                    </div>
                </div>

                <h2 class="section-title">Our Mission</h2>
                <p class="content-text">
                    To democratize access to powerful calculation tools by providing free, accurate, and easy-to-use online calculators that serve students, educators, researchers, and professionals worldwide.
                </p>

                <h2 class="section-title">Our Values</h2>
                <div class="values-grid">
                    <div class="value-card">
                        <div class="value-icon">🆓</div>
                        <h3 class="value-title">Always Free</h3>
                        <p class="value-description">We believe knowledge and tools should be accessible to everyone, regardless of their financial situation.</p>
                    </div>
                    <div class="value-card">
                        <div class="value-icon">🎯</div>
                        <h3 class="value-title">Accuracy First</h3>
                        <p class="value-description">Every calculator is rigorously tested and based on scientifically proven formulas and methods.</p>
                    </div>
                    <div class="value-card">
                        <div class="value-icon">🌍</div>
                        <h3 class="value-title">Global Access</h3>
                        <p class="value-description">Our tools work on any device, anywhere in the world, with no downloads or installations required.</p>
                    </div>
                    <div class="value-card">
                        <div class="value-icon">🔒</div>
                        <h3 class="value-title">Privacy Focused</h3>
                        <p class="value-description">We respect your privacy and don't store your calculation data or personal information.</p>
                    </div>
                </div>

                <h2 class="section-title">What We Offer</h2>
                <p class="content-text">
                    Our comprehensive suite of calculators covers six major categories:
                </p>
                <ul style="color: rgb(75, 82, 89); line-height: 1.6; margin-bottom: 20px; padding-left: 20px;">
                    <li><strong>Biology Calculators:</strong> From biological age to AP Biology scoring, molecular biology calculations, and more</li>
                    <li><strong>Chemistry Calculators:</strong> Molarity, molecular weight, pH calculations, and chemical equation balancing</li>
                    <li><strong>Mathematics Calculators:</strong> Algebra, calculus, statistics, geometry, and advanced mathematical functions</li>
                    <li><strong>Physics Calculators:</strong> Force, energy, motion, wave calculations, and fundamental physics principles</li>
                    <li><strong>Health Calculators:</strong> BMI, BMR, calorie counting, and various health assessment tools</li>
                    <li><strong>Finance Calculators:</strong> Loan calculations, mortgage planning, investment analysis, and retirement planning</li>
                </ul>

                <div class="team-section">
                    <h2 class="section-title">Our Team</h2>
                    <p class="content-text">
                        Behind v.calculator.city is a dedicated team of experts passionate about making complex calculations accessible to everyone.
                    </p>
                    <div class="team-grid">
                        <div class="team-card">
                            <div class="team-avatar">DR</div>
                            <h3 class="team-name">Dr. Sarah Chen</h3>
                            <p class="team-role">Lead Mathematician</p>
                            <p class="team-description">PhD in Applied Mathematics with 15+ years of experience in algorithm development and scientific computing.</p>
                        </div>
                        <div class="team-card">
                            <div class="team-avatar">MJ</div>
                            <h3 class="team-name">Michael Johnson</h3>
                            <p class="team-role">Senior Developer</p>
                            <p class="team-description">Full-stack developer specializing in web applications and user experience optimization.</p>
                        </div>
                        <div class="team-card">
                            <div class="team-avatar">EP</div>
                            <h3 class="team-name">Dr. Emily Parker</h3>
                            <p class="team-role">Scientific Advisor</p>
                            <p class="team-description">Professor of Chemistry with expertise in computational chemistry and educational technology.</p>
                        </div>
                    </div>
                </div>

                <!-- Advertisement -->
                <div style="background: rgb(247, 249, 255); border-radius: 12px; padding: 30px; margin: 40px 0; text-align: center; border: 1px solid rgb(210, 220, 255);">
                    <div style="font-size: 12px; color: rgb(116, 128, 145); margin-bottom: 15px; text-transform: uppercase; letter-spacing: 1px;">Advertisement</div>
                    <ins class="adsbygoogle"
                         style="display:block"
                         data-ad-client="ca-pub-2205593928173688"
                         data-ad-slot="2222222222"
                         data-ad-format="auto"
                         data-full-width-responsive="true"></ins>
                    <script>
                         (adsbygoogle = window.adsbygoogle || []).push({});
                    </script>
                </div>

                <h2 class="section-title">Join Our Community</h2>
                <p class="content-text">
                    We're more than just a calculator website – we're a community of learners, educators, and professionals who believe in the power of accessible tools. Whether you're a student working on homework, a researcher conducting experiments, or a professional making important calculations, we're here to help.
                </p>
                <p class="content-text">
                    Have a suggestion for a new calculator? Found a bug? Want to contribute to our mission? We'd love to hear from you. <a href="/contact" style="color: #3B68FC; text-decoration: none;">Get in touch</a> and be part of our growing community.
                </p>
            </div>
        </main>

        <footer class="footer">
            <div class="footer-container">
                <div class="footer-base">
                    <span class="footer-copyright">© 2024 v.calculator.city. All rights reserved.</span>
                    <div style="display: flex; gap: 16px;">
                        <a href="/privacy-policy" class="footer-base-link">Privacy Policy</a>
                        <a href="/terms-of-service" class="footer-base-link">Terms of Service</a>
                        <a href="/contact" class="footer-base-link">Contact</a>
                    </div>
                </div>
            </div>
        </footer>
    </div>
</body>
</html>
