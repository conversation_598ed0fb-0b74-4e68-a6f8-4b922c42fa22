#!/usr/bin/env python3
"""
Domain Update Script for v.calculator.city
Updates all references from z.calculator.city to v.calculator.city
"""

import os
import re

def update_file_content(file_path, old_domain, new_domain):
    """Update domain references in a file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # Replace all instances of the old domain with the new domain
        updated_content = content.replace(old_domain, new_domain)
        
        # Also update social media handles
        updated_content = updated_content.replace('@zcalculatorcity', '@vcalculatorcity')
        updated_content = updated_content.replace('/zcalculatorcity', '/vcalculatorcity')
        
        # Write back if changes were made
        if content != updated_content:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(updated_content)
            print(f"✅ Updated: {file_path}")
            return True
        else:
            print(f"⏭️  No changes needed: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ Error updating {file_path}: {e}")
        return False

def main():
    """Main function to update all files"""
    old_domain = "z.calculator.city"
    new_domain = "v.calculator.city"
    
    # List of files to update
    files_to_update = [
        "sitemap.xml",
        "robots.txt",
        "privacy-policy.html",
        "terms-of-service.html",
        "contact.html",
        "about.html",
        "404.html",
        "site.webmanifest",
        ".htaccess",
        "SEO-OPTIMIZATION-REPORT.md",
        "DEPLOYMENT-CHECKLIST.md",
        "ADSENSE-SETUP.md"
    ]
    
    print(f"🔄 Updating domain from {old_domain} to {new_domain}")
    print("=" * 60)
    
    updated_count = 0
    
    for file_path in files_to_update:
        if os.path.exists(file_path):
            if update_file_content(file_path, old_domain, new_domain):
                updated_count += 1
        else:
            print(f"⚠️  File not found: {file_path}")
    
    print("=" * 60)
    print(f"✅ Domain update complete! Updated {updated_count} files.")
    print(f"🌐 New domain: {new_domain}")
    
    # Create a summary report
    with open("DOMAIN-UPDATE-REPORT.md", "w", encoding="utf-8") as report:
        report.write(f"""# Domain Update Report

## Summary
- **Old Domain**: {old_domain}
- **New Domain**: {new_domain}
- **Files Updated**: {updated_count}
- **Update Date**: December 19, 2024

## Files Updated
""")
        for file_path in files_to_update:
            if os.path.exists(file_path):
                report.write(f"- ✅ {file_path}\n")
            else:
                report.write(f"- ❌ {file_path} (not found)\n")
        
        report.write(f"""
## Changes Made
1. All instances of `{old_domain}` replaced with `{new_domain}`
2. Social media handles updated from `@zcalculatorcity` to `@vcalculatorcity`
3. Social media URLs updated from `/zcalculatorcity` to `/vcalculatorcity`

## Next Steps
1. Update DNS settings to point {new_domain} to your server
2. Update Google Search Console with new domain
3. Update Google Analytics property
4. Update AdSense account with new domain
5. Update social media profiles with new domain
6. Set up 301 redirects from old domain (if applicable)

## SEO Considerations
- Update canonical URLs
- Submit new sitemap to search engines
- Monitor search rankings during transition
- Update backlinks where possible
""")
    
    print("📄 Created DOMAIN-UPDATE-REPORT.md with detailed information")

if __name__ == "__main__":
    main()
