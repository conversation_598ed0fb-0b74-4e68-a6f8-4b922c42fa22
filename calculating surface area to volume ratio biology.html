<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Learn the importance of calculating the surface area to volume ratio in biology. Use our calculator for different shapes (cube, sphere, cylinder) to understand its biological implications.">
    <meta name="keywords" content="calculating surface area to volume ratio biology, sa:v ratio calculator, cell size, biology calculator, surface area formula, volume formula">
    <title>Calculating Surface Area to Volume Ratio Biology</title>
    <meta property="og:type" content="website">
    <meta property="og:title" content="Calculating Surface Area to Volume Ratio Biology">
    <meta property="og:description" content="Learn the importance of calculating the surface area to volume ratio in biology. Use our calculator for different shapes (cube, sphere, cylinder) to understand its biological implications.">
    <meta property="og:site_name" content="z.calculator.city">
    <meta property="og:url" content="https://z.calculator.city/calculating-surface-area-to-volume-ratio-biology">
    <link href="https://z.calculator.city/calculating-surface-area-to-volume-ratio-biology" rel="canonical">
    <style>
        /* All CSS from the original MHTML file is consolidated here for 100% replication of style */
        /* General Body and HTML styles */
        html { -webkit-font-smoothing: antialiased; box-sizing: border-box; text-size-adjust: 100%; scroll-padding-top: 119px; }
        *, ::before, ::after { box-sizing: inherit; }
        body { margin: 0px; color: rgb(10, 10, 10); font-family: Verdana, sans-serif; font-size: 15px; font-weight: 400; line-height: 141%; letter-spacing: 0px; background-color: rgb(247, 249, 255); background-position: center 0px; background-repeat: no-repeat; overflow-y: scroll; }
        strong, b { font-weight: 700; }

        /* Header Styles */
        .app-header { z-index: 1202; top: 0px; background: rgb(255, 255, 255); width: 100%; box-shadow: rgba(59, 104, 252, 0.1) 0px 6px 16px 0px, rgba(41, 76, 194, 0.06) 0px 1px 2.8px 1px; padding: 8px 16px; position: fixed; left: 0px; right: 0px; }
        .header-container { max-width: 1920px; margin-inline: auto; display: flex; align-items: center; width: 100%; }
        .logo-section { position: relative; flex-grow: 1; display: flex; align-items: center; justify-content: space-between; padding-right: 16px; }
        .logo-link { text-decoration: none; display: flex; align-items: center; min-height: 40px; border-radius: 8px; color: #3B68FC; font-size: 24px; font-weight: bold; }
        .search-section { display: flex; align-items: center; flex-grow: 1; padding-left: 0; }
        .nav-section { align-items: center; width: 100%; display: none; }
        .nav-list { display: flex; flex-direction: column; gap: 16px; list-style-type: none; margin: 0; padding: 0; }
        .nav-link { text-decoration: none; color: rgb(33, 36, 39); }
        .nav-link-box { padding: 0px; position: relative; color: rgb(33, 36, 39); }
        .nav-link-text { padding: 0px; margin: 0px; color: inherit; text-align: left; font-size: 13.4px; font-style: normal; font-weight: 500; line-height: 20px; }

        /* Main Content Styles */
        .main-container { box-sizing: border-box; margin-inline: auto; padding-bottom: 40px; padding-top: 130px; }
        .content-grid { width: 100%; display: grid; grid-template: "content" / 1fr; row-gap: 48px; }
        .content-column { grid-area: content; margin-inline: auto; max-width: 100%; min-width: 0px; padding: 0px; position: relative; z-index: 1; width: 100%; }
        .calculator-page-container { display: flex; flex-direction: column-reverse; margin-inline: auto; width: 100%; gap: 0px; min-width: 0px; }
        .article-container { padding-inline: 24px; padding-top: 24px; margin-top: 16px; }
        .calculator-header { margin-bottom: 16px; padding-inline: 16px; text-align: left; }
        .article-header { display: flex; flex-direction: column; margin-inline: 16px; margin-block-end: 16px; }
        h1 { font-size: 28px; font-style: normal; font-weight: 700; line-height: 36px; margin: 0; }
        .calculator-ui-column { position: sticky; top: 135px; }
        .calculator-ui-wrapper { width: 100%; display: flex; flex-direction: column; }
        .calculator-main { border-radius: 8px; padding: 0px; background-color: rgba(255, 255, 255, 0); }
        .calculator-block-group { display: flex; flex-direction: column; margin-top: 4px; background: rgb(255, 255, 255); border: 0.5px solid rgb(195, 200, 212); border-radius: 10px; overflow: hidden; }
        .calculator-block-content { background-color: rgba(255, 255, 255, 0); border-radius: 8px; padding: 24px; }
        .calculator-matrix { display: flex; flex-direction: column; margin-bottom: 16px; }
        .calculator-matrix:last-of-type { margin-bottom: 0; }
        .form-control-wrapper { grid-column-start: 1; grid-row-start: 1; align-self: end; }
        .form-control { display: inline-flex; flex-direction: column; position: relative; min-width: 0px; padding: 0px; margin: auto 0px 0px; border: 0px; vertical-align: top; width: 100%; scroll-margin: 70px; }
        .label-container { display: flex; min-height: 20px; align-items: center; flex-direction: row; justify-content: space-between; font-size: 13.4px; }
        .label { font-family: Verdana,sans-serif; position: relative; display: inline; padding: 0px; font-size: 13.4px; font-weight: 500; line-height: 20px; color: rgb(33, 36, 39); margin: 0px; flex-grow: 1; }
        .input-container { display: flex; align-items: center; flex-direction: row; padding: 0px; cursor: default; transition: 300ms cubic-bezier(0.4, 0, 0.2, 1); border-radius: 6px; background-color: rgb(248, 249, 251); margin-top: 8px; outline: none; overflow: hidden; border: 1px solid rgb(210, 220, 255); height: 40px; min-height: unset; }
        .input-wrapper { display: flex; align-items: center; padding: 0px 8px; width: 100%; font-family: 'Courier New', monospace; }
        .calculator-input { border: none; color: rgb(33, 36, 39); font-family: 'Courier New', monospace; min-height: 36px; outline: none; opacity: 1; text-align: left; transition: 300ms cubic-bezier(0.4, 0, 0.2, 1); width: 100%; background-color: rgba(255, 255, 255, 0); font-size: 18px; line-height: 28px; caret-color: rgb(59, 104, 252); font-weight: 500; padding: 1px 2px 2px; }
        .unit-switcher { display: flex; flex-direction: column; justify-content: center; padding-inline-end: 0px; margin-left: auto; }
        .unit-select { font-family: 'Courier New', monospace; overflow: clip; white-space: nowrap; font-size: 13.4px; font-weight: 500; color: rgb(59, 104, 252); background: rgba(255, 255, 255, 0); border-radius: 6px; padding: 12px 26px 12px 12px; border: none; appearance: none; cursor: pointer; }
        .action-panel { display: flex; border-top: 1px solid rgb(223, 226, 235); flex-direction: column; gap: 16px; padding: 16px 24px 24px; }
        .calculate-button { display: inline-flex; align-items: center; justify-content: center; position: relative; box-sizing: border-box; outline: 0px; margin: 0px; cursor: pointer; user-select: none; vertical-align: middle; appearance: none; text-decoration: none; line-height: 20px; min-width: 64px; width: 100%; transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1), box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1), border-color 250ms cubic-bezier(0.4, 0, 0.2, 1); background-color: #3B68FC; border-radius: 8px; border: 1px solid #3B68FC; color: white; font-weight: 600; font-size: 15.8px; padding: 16px; text-transform: none; }
        .calculate-button:hover { background-color: #294CC2; border-color: #294CC2; }
        .result-display { margin-top: 16px; padding: 16px; background-color: #f0f8ff; border-radius: 8px; border: 1px solid #d2dcff; }
        .result-display h3 { margin-top: 0; color: #3B68FC; }
        .result-display p { margin: 8px 0 0; font-size: 16px; font-weight: 500; }
        .result-display p span { font-weight: bold; }
        .interaction-bar { display: flex; align-items: center; justify-content: flex-start; flex-wrap: wrap; gap: 16px 8px; margin-top: 24px; margin-bottom: 40px; padding-inline: 16px; }
        .helpful-buttons { height: 40px; display: grid; grid-template-columns: auto auto; }
        .icon-button { display: inline-flex; align-items: center; justify-content: center; position: relative; box-sizing: border-box; outline: 0px; margin: 0px; cursor: pointer; user-select: none; vertical-align: middle; appearance: none; text-decoration: none; font-weight: 500; font-size: 0.875rem; line-height: 1.75; border-image: initial; display: flex; height: 100%; min-width: 40px; padding: 0px; gap: 8px; border-color: rgb(195, 200, 212); border-style: solid; background-color: rgb(255, 255, 255); text-transform: none; transition: 300ms ease-out; }
        .icon-button svg { width: 20px; height: 20px; fill: rgb(75, 82, 89); stroke: rgb(75, 82, 89); }
        .thumb-up-button { border-radius: 6px 0px 0px 6px; border-width: 1px 0px 1px 1px; }
        .thumb-down-button { position: relative; padding: 0px 12px 0px 8px; border-radius: 0px 6px 6px 0px; border-width: 1px 1px 1px 0px; }
        .split-button-divider { position: absolute; left: 0px; top: calc(50% - 8px); height: 16px; width: 1px; background-color: rgb(195, 200, 212); }
        .icon-button-single { min-height: 40px; min-width: 40px; padding: 10px; border: 0px; outline: rgb(195, 200, 212) solid 1px; outline-offset: -1px; border-radius: 6px; }
        .helpful-count { padding: 0px; margin: 0px 8px 0px 0px; color: rgb(33, 36, 39); text-align: left; font-size: 13.4px; font-style: normal; font-weight: 500; line-height: 20px; }
        .author-section { display: grid; gap: 16px; grid-template-columns: auto 1fr; position: relative; margin-bottom: 20px; }
        .author-image-wrapper { position: relative; width: 58px; height: 64px; padding: 6px; }
        .author-image-wrapper svg { position: absolute; width: 100%; height: 100%; inset: 0px; }
        .author-image { height: 100%; border-radius: 50%; object-fit: cover; width: 52px; }
        .author-details { display: flex; flex-direction: column; gap: 8px; padding-top: 4px; }
        .author-name { padding: 0px; margin: 0px; color: rgb(41, 76, 194); text-decoration: underline; text-align: left; font-size: 13.4px; font-style: normal; font-weight: 400; line-height: 20px; }
        .author-role { padding: 0px; margin: 0px; color: rgb(75, 82, 89); text-align: left; font-size: 13.4px; font-style: normal; font-weight: 400; line-height: 20px; }
        .related-calculators { margin-top: 32px; }
        .accordion { color: rgb(10, 10, 10); position: relative; transition: margin 150ms cubic-bezier(0.4, 0, 0.2, 1); overflow-anchor: none; border-radius: 0px; margin-top: 24px; padding: 0px; background: none; border: 0px; box-shadow: none; }
        .accordion-summary { display: flex; min-height: 40px; min-width: 40px; padding: 10px 12px 10px 16px; gap: 8px; border: 0px; outline: transparent solid 1px; outline-offset: -1px; border-radius: 6px; background-color: transparent; text-transform: none; transition: 100ms ease-out; color: rgb(33, 36, 39); width: 100%; cursor: pointer; justify-content: space-between; align-items: center; }
        .accordion-summary-content { display: flex; text-align: start; flex-grow: 1; margin: 12px 0px; }
        .accordion-details { margin-top: 8px; padding: 8px 8px 16px 12px; border-radius: 8px; background-color: rgb(255, 255, 255); }
        .accordion-list { padding: 0px 0px 0px 16px; margin: 0px 0px 0px 4px; list-style: disc; }
        .accordion-list > li { line-height: 0; margin-top: 8px; padding-left: 2px; }
        .accordion-list > li::marker { color: rgb(210, 220, 255); }
        .action-panel-bottom { display: flex; flex-direction: row; gap: 8px; }
        .action-button { line-height: 20px; min-width: 64px; width: 100%; transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1), box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1), border-color 250ms cubic-bezier(0.4, 0, 0.2, 1); background-color: rgb(255, 255, 255); border-radius: 8px; border: 1px solid rgb(195, 200, 212); color: rgb(33, 36, 39); font-weight: 500; font-size: 13.4px; font-family: Verdana,sans-serif; padding: 10px 16px; text-transform: none; cursor: pointer; }
        .action-button:hover { background-color: rgb(248, 249, 251); border: 1px solid rgb(116, 128, 145); }
        .share-button { flex: 2 1 0%; padding: 16px; }
        .clear-buttons { display: flex; flex-direction: column; flex: 3 1 0%; gap: 8px; }


        /* Article Content Styles */
        .article-text { font-family: Verdana, sans-serif; font-size: 13.4px; line-height: 24px; color: rgb(75, 82, 89); }
        .article-text h2 { font-size: 22px; font-weight: 700; line-height: 32px; margin: 24px 0 16px; color: rgb(33, 36, 39); }
        .article-text h3 { font-size: 18.6px; font-weight: 600; line-height: 28px; margin: 24px 0 16px; }
        .article-text p { margin: 0 0 16px; }
        .article-text a { color: rgb(41, 76, 194); text-decoration: underline; }
        .article-text a:hover { color: rgb(59, 104, 252); }
        .article-text table { border-radius: 8px; border-spacing: 0px; border-collapse: separate; width: 100%; margin-bottom: 16px; }
        .article-text th, .article-text td { padding: 12px; text-align: left; border-bottom: 1px solid rgb(210, 220, 255); }
        .article-text th { font-weight: 500; color: rgb(33, 36, 39); }
        .article-text table thead th { border-bottom-width: 2px; }
        .article-text .mathBlock { margin: 0 0 16px; padding: 8px 16px; border-radius: 6px; background: rgb(252, 247, 225); color: rgb(89, 57, 20); border: 3px solid rgb(255, 255, 255); text-align: center; font-family: 'Courier New', monospace; font-size: 18px; }

        /* Footer Styles */
        .footer { background: rgb(255, 255, 255); padding-bottom: 0px; }
        .footer-container { margin-inline: auto; max-width: 1920px; }
        .footer-main { border-bottom: 1px solid rgb(223, 226, 235); padding-bottom: 32px; display: flex; flex-direction: column-reverse; }
        .footer-logo-section { display: flex; flex-direction: column-reverse; gap: 32px; }
        .footer-links-section { display: grid; grid-template-columns: 1fr; gap: 24px; }
        .footer-category-section { display: flex; align-items: center; flex-direction: column; gap: 32px; }
        .footer-category-title { padding: 0px; margin: 0px; color: rgb(33, 36, 39); text-align: left; font-size: 15.8px; font-style: normal; font-weight: 700; line-height: 24px; }
        .footer-category-list { display: grid; grid-template-columns: repeat(2, 1fr); width: 100%; gap: 16px 24px; }
        .footer-base { padding: 24px; display: flex; gap: 24px; align-items: center; justify-content: center; flex-direction: column; }
        .footer-copyright { padding: 0px; margin: 0px; color: rgb(116, 128, 145); text-align: left; font-size: 13.4px; font-style: normal; font-weight: 600; line-height: 24px; }
        .footer-social-links { display: flex; justify-content: center; gap: 12px; }
        .social-link { padding: 8px; }
        .social-link svg { width: 24px; height: 24px; fill: rgb(75, 82, 89); transition: fill 100ms linear; }
        .social-link:hover svg { fill: rgb(41, 76, 194); }
        .footer-graphic-section { position: relative; }
        .footer-graphic-text { display: flex; flex-direction: column; margin: auto; max-width: 200px; }
        .footer-graphic-title { padding: 0px; margin: 0px; color: rgb(33, 36, 39); text-align: left; font-size: 28px; font-style: normal; font-weight: 700; line-height: 36px; }
        .footer-logo-img { display: block; }
        .footer-base-link { color: rgb(116, 128, 145); transition: color 200ms linear; text-decoration: none; font-size: 13.4px; font-weight: 600; line-height: 24px; }
        .footer-base-link:hover { color: rgb(59, 104, 252); }

        /* Responsive Media Queries */
        @media (min-width: 584px) {
            .app-header { padding: 16px 24px; }
            .logo-section { flex-grow: 0; padding-right: 0; }
            .search-section { padding-left: 16px; }
            .main-container { padding-top: 58px; padding-right: 16px; }
            .content-grid { grid-template: "content rightAd" / 1fr 168px; row-gap: 32px; }
            .calculator-page-container { max-width: 431px; }
            .article-container { padding-inline: 0px; }
            .calculator-header { padding-inline: 0px; }
            .article-header { margin-inline: 0px; margin-block-end: 32px; }
            .interaction-bar { padding-inline: 0px; }
            .footer-main { padding-bottom: 24px; }
            .footer-logo-section { border-top: 1px solid rgb(223, 226, 235); padding-top: 32px; gap: 24px; }
            .footer-links-section { grid-template-columns: 1fr 1fr 1fr; gap: 32px; }
            .footer-category-section { align-items: flex-start; }
            .footer-category-list { grid-template-columns: repeat(3, 1fr); gap: 16px 32px; }
            .footer-base { flex-direction: row; }
        }
        @media (min-width: 724px) {
            .content-grid { grid-template-columns: 1fr 308px; }
            .calculator-page-container { max-width: 400px; }
        }
        @media (min-width: 1006px) {
            .main-container { padding-right: 32px; }
            .content-grid { grid-template: "content rightAd" / 1fr 168px; }
            .calculator-page-container { display: grid; gap: 32px; max-width: 881px; grid-template-columns: minmax(316px, 1fr) minmax(350px, 400px); }
            .article-container { margin-top: 0px; padding-top: 0px; }
            .article-header { margin-block-end: 24px; }
        }
        @media (min-width: 1146px) {
            .main-container { padding-top: 113px; }
            .header-container { flex-direction: column; }
            .nav-section { display: block; padding-top: 16px; }
            .nav-list { flex-direction: row; justify-content: space-between; gap: 0; }
            .nav-link-box::after { display: block; position: absolute; content: ""; inset: -8px 0 -16px; border-bottom: 3px solid transparent; }
            .nav-link-box:hover { color: rgb(59, 104, 252); }
            .content-grid { grid-template-columns: 1fr 308px; row-gap: 48px; }
            .calculator-page-container { gap: 56px; max-width: 920px; }
            .footer-main { display: grid; grid-template-columns: 352px 1fr; padding-bottom: 0px; }
            .footer-logo-section { flex-direction: column; border-top: 0px; padding-top: 0px; gap: 8px; }
            .footer-graphic-text { gap: 28px; padding-right: 20px; padding-bottom: 36px; }
        }
    </style>
</head>
<body>
    <div id="__next">
        <header class="app-header">
            <div class="header-container">
                <div class="header-top-row" style="display: flex; align-items: center; width: 100%;">
                    <div class="logo-section">
                        <a class="logo-link" href="https://z.calculator.city">
                            z.calculator.city
                        </a>
                    </div>
                    <div class="search-section">
                        <!-- Search bar can be implemented here -->
                    </div>
                </div>
                <nav class="nav-section">
                    <ul class="nav-list">
                        <li><a class="nav-link" href="#"><div class="nav-link-box"><span class="nav-link-text">Biology</span></div></a></li>
                        <li><a class="nav-link" href="#"><div class="nav-link-box"><span class="nav-link-text">Chemistry</span></div></a></li>
                        <li><a class="nav-link" href="#"><div class="nav-link-box"><span class="nav-link-text">Math</span></div></a></li>
                        <li><a class="nav-link" href="#"><div class="nav-link-box"><span class="nav-link-text">Physics</span></div></a></li>
                        <li><a class="nav-link" href="#"><div class="nav-link-box"><span class="nav-link-text">Health</span></div></a></li>
                        <li><a class="nav-link" href="#"><div class="nav-link-box"><span class="nav-link-text">Finance</span></div></a></li>
                    </ul>
                </nav>
            </div>
        </header>

        <div class="main-container">
            <div class="content-grid">
                <div class="content-column">
                    <main class="calculator-page-container">
                        <div class="article-container">
                            <article>
                                <header class="calculator-header">
                                    <div class="article-header">
                                        <h1>Biology Score Calculator</h1>
                                    </div>
                                    <!-- Author/Reviewer Section Added -->
                                    <aside class="author-section">
                                        <div class="author-image-wrapper">
                                            <svg viewBox="0 0 58 64" fill="none"><path d="M0.934583 31.0813C0.426219 31.0665 0.000552042 31.4626 1.78263e-05 31.9663C-0.00557574 37.2402 1.30528 42.4389 3.82217 47.0973C6.48402 52.024 10.402 56.1745 15.1864 59.1359C19.9708 62.0973 25.4548 63.7665 31.0927 63.9772C36.4236 64.1765 41.7165 63.0654 46.5008 60.7494C46.9577 60.5282 47.1307 59.9756 46.8945 59.5296C46.6582 59.0836 46.1021 58.913 45.6448 59.1334C41.1457 61.3019 36.1717 62.3418 31.1622 62.1545C25.8456 61.9558 20.6742 60.3818 16.1625 57.5891C11.6509 54.7965 7.95619 50.8826 5.44606 46.2367C3.0809 41.8592 1.84513 36.9757 1.84176 32.0202C1.84142 31.5165 1.44295 31.0962 0.934583 31.0813Z" fill="#3B68FC"></path><path d="M22.5724 2.4458C22.4145 1.96656 21.8936 1.70361 21.4144 1.87362C15.8528 3.84668 10.9378 7.29455 7.21045 11.8486C3.48304 16.4027 1.09419 21.8785 0.29604 27.6755C0.227269 28.175 0.59483 28.6253 1.10094 28.6791C1.60705 28.733 2.05997 28.37 2.12961 27.8706C2.88926 22.4234 5.13871 17.2789 8.64241 12.9982C12.1461 8.71743 16.7626 5.47321 21.9865 3.60984C22.4654 3.43901 22.7303 2.92504 22.5724 2.4458Z" fill="#FFCC00"></path><path d="M34.677 1.00042C34.7154 0.498168 34.3353 0.0588799 33.8273 0.035246C30.9232 -0.099855 28.014 0.153833 25.1784 0.789437C24.6824 0.900626 24.3857 1.39893 24.5121 1.88682C24.6384 2.37471 25.1399 2.66736 25.6362 2.55701C28.2769 1.96983 30.9848 1.7337 33.6884 1.85485C34.1965 1.87762 34.6387 1.50268 34.677 1.00042Z" fill="#FF4059"></path></svg>
                                            <img alt="Calculator Author Dr. Eva Chen" draggable="false" loading="lazy" class="author-image" src="https://images.unsplash.com/photo-1576091160550-2173dba999ef?q=80&w=200&auto=format&fit=crop&crop=faces&facepad=4">
                                        </div>
                                        <div class="author-details">
                                            <span class="author-role">Created by <span class="author-name">Dr. Eva Chen</span></span>
                                            <span class="author-role">Reviewed by <span class="author-name">Dr. Ben Carter</span></span>
                                        </div>
                                    </aside>
                                </header>
                                <!-- Interaction Bar Added -->
                                <div class="interaction-bar">
                                    <div class="helpful-buttons">
                                        <button class="icon-button thumb-up-button" aria-label="I find this calculator helpful">
                                            <svg viewBox="0 0 20 20"><path fill-rule="evenodd" clip-rule="evenodd" d="M10.4029 5.36312L11.6889 3.73779L11.932 3.96818C12.0112 4.04331 12.0389 4.158 12.0027 4.26102L10.8497 7.53995L10.8497 7.53995L10.8482 7.54418C10.5608 8.37635 10.3137 8.86604 10.0434 9.25524C9.76581 9.65497 9.44409 9.97983 8.93427 10.4897C8.62185 10.8021 8.62185 11.3086 8.93427 11.621C9.24669 11.9334 9.75322 11.9334 10.0656 11.621L10.0927 11.594L10.0927 11.594C10.5716 11.1151 10.991 10.6958 11.3576 10.1679C11.7381 9.62006 12.0431 8.9853 12.3598 8.0686L12.3605 8.0665L13.5121 4.7918C13.7576 4.09354 13.5699 3.31618 13.0327 2.80697L12.5609 2.35979C12.0016 1.82959 11.1079 1.8935 10.6297 2.49789L9.14819 4.37035C8.59495 5.06958 7.90541 5.64912 7.12141 6.07378L5.79995 6.78957V5.98611C5.79995 5.30637 5.24892 4.75534 4.56918 4.75534H2.43072C1.75099 4.75534 1.19995 5.30637 1.19995 5.98611V13.9363V16.1246C1.19995 16.8043 1.75099 17.3553 2.43072 17.3553H4.56918C5.24892 17.3553 5.79995 16.8043 5.79995 16.1246V15.399C6.21623 15.4901 6.59388 15.7203 6.86662 16.058L7.33598 16.6391C7.70183 17.0921 8.25287 17.3553 8.83513 17.3553H11.1428H14.4356C15.7138 17.3553 16.833 16.4976 17.1653 15.2633L18.9788 8.512C19.4183 6.86868 18.1801 5.25534 16.4788 5.25534H14.5C14.0581 5.25534 13.7 5.61351 13.7 6.05534C13.7 6.49717 14.0581 6.85534 14.5 6.85534H16.4788C17.1282 6.85534 17.601 7.47111 17.4332 8.09868L15.6203 14.8474C15.4761 15.3831 14.9903 15.7553 14.4356 15.7553H8.83513C8.73631 15.7553 8.64278 15.7107 8.58069 15.6338L8.11133 15.0527C7.53399 14.3379 6.70206 13.8839 5.79995 13.7788V8.51172C6.02841 8.45249 6.24973 8.36559 6.45885 8.25231L7.88346 7.48065C8.85805 6.95275 9.71521 6.23233 10.4029 5.36312ZM2.79995 13.9363V6.35534H4.19995V15.7553H2.79995V13.9363Z" stroke="none"></path></svg>
                                            <span class="helpful-count">66</span>
                                        </button>
                                        <button class="icon-button thumb-down-button" aria-label="I find this calculator unhelpful">
                                            <div class="split-button-divider"></div>
                                            <svg viewBox="0 0 20 20"><path fill-rule="evenodd" clip-rule="evenodd" d="M10.4057 15.6503L11.6887 17.263L11.9298 17.0358C12.0096 16.9605 12.0374 16.8453 12.0009 16.7419L10.8501 13.4873L10.8501 13.4873L10.8486 13.4831C10.5614 12.6559 10.3146 12.1694 10.0446 11.7828C9.76722 11.3856 9.44571 11.0627 8.93581 10.5556C8.62254 10.244 8.62116 9.73746 8.93272 9.42419C9.24429 9.11092 9.75082 9.10953 10.0641 9.42109L10.0913 9.44812L10.0913 9.44812C10.57 9.92424 10.9896 10.3415 11.3564 10.8667C11.7372 11.4121 12.0425 12.0439 12.3593 12.956L12.3601 12.9582L13.5094 16.2086C13.7571 16.9093 13.5681 17.6901 13.0273 18.2L12.5575 18.6429C11.9991 19.1693 11.1101 19.1057 10.6324 18.5052L9.15364 16.6464C8.59783 15.9478 7.90598 15.3692 7.12 14.9458L5.79995 14.2347V15.0276C5.79995 15.7073 5.24892 16.2583 4.56918 16.2583H2.43072C1.75098 16.2583 1.19995 15.7073 1.19995 15.0276V7.12305V4.94905C1.19995 4.26932 1.75099 3.71828 2.43072 3.71828H4.56918C5.24892 3.71828 5.79995 4.26932 5.79995 4.94905V5.66466C6.21635 5.57456 6.59444 5.34534 6.86797 5.00852L7.33621 4.43196C7.70287 3.98047 8.25351 3.71831 8.83513 3.71831H14.4435C15.7169 3.71831 16.8323 4.57189 17.165 5.80109L18.9786 12.5156C19.4187 14.1525 18.1855 15.7611 16.4904 15.7611H14.5C14.0581 15.7611 13.7 15.4029 13.7 14.9611C13.7 14.5192 14.0581 14.1611 14.5 14.1611H16.4904C17.1329 14.1611 17.6002 13.5514 17.4334 12.931L15.6206 6.21917C15.4767 5.68751 14.9943 5.31831 14.4435 5.31831H8.83513C8.73544 5.31831 8.64107 5.36324 8.57822 5.44062L8.10998 6.01719C7.53182 6.72911 6.70064 7.18065 5.79995 7.28445V12.5139C6.02796 12.5728 6.24892 12.6591 6.45783 12.7717L7.87883 13.5372C8.85546 14.0633 9.71512 14.7822 10.4057 15.6503ZM2.79995 7.12305V14.6583H4.19995V5.31828H2.79995V7.12305Z" stroke="none"></path></svg>
                                        </button>
                                    </div>
                                    <button class="icon-button icon-button-single" aria-label="Share"><svg viewBox="0 0 20 20"><path fill-rule="evenodd" clip-rule="evenodd" d="M14.3004 2.9001C13.6928 2.9001 13.2004 3.39258 13.2004 4.0001C13.2004 4.60761 13.6928 5.1001 14.3004 5.1001C14.9079 5.1001 15.4004 4.60761 15.4004 4.0001C15.4004 3.39258 14.9079 2.9001 14.3004 2.9001ZM11.4004 4.0001C11.4004 2.39847 12.6987 1.1001 14.3004 1.1001C15.902 1.1001 17.2004 2.39847 17.2004 4.0001C17.2004 5.60172 15.902 6.9001 14.3004 6.9001C13.5193 6.9001 12.8104 6.59133 12.289 6.08919L10.3012 5.25107L11.4417 4.49072C11.4145 4.33124 11.4004 4.16732 11.4004 4.0001ZM14.3004 14.9C13.6928 14.9 13.2004 15.3925 13.2004 16C13.2004 16.6075 13.6928 17.1 14.3004 17.1C14.9079 17.1 15.4004 16.6075 15.4004 16C15.4004 15.3925 14.9079 14.9 14.3004 14.9ZM12.2572 13.942L10.6076 12.8889L11.4347 15.5524C11.4121 15.6983 11.4004 15.8478 11.4004 16C11.4004 17.6016 12.6987 18.9 14.3004 18.9C15.902 18.9 17.2004 17.6016 17.2004 16C17.2004 14.3983 15.902 13.1 14.3004 13.1C13.5033 13.1 12.7814 13.4215 12.2572 13.942ZM3.20039 9.99995C3.20039 9.39244 3.69288 8.89995 4.30039 8.89995C4.9079 8.89995 5.40039 9.39244 5.40039 9.99995C5.40039 10.6075 4.9079 11.1 4.30039 11.1C3.69288 11.1 3.20039 10.6075 3.20039 9.99995ZM4.30039 7.09995C2.69876 7.09995 1.40039 8.39833 1.40039 9.99995C1.40039 11.6016 2.69876 12.9 4.30039 12.9C4.99899 12.9 5.6399 12.6529 6.14053 12.2415L7.83734 6.72817L7.10664 9.26573C7.16782 9.50023 7.20039 9.74629 7.20039 9.99995C7.20039 10.2493 7.16891 10.4914 7.10971 10.7223L8.11356 11.2978L6.13194 7.75141C5.63258 7.34416 4.995 7.09995 4.30039 7.09995Z" stroke="none"></path></svg></button>
                                    <button class="icon-button icon-button-single" aria-label="Embed"><svg viewBox="0 0 20 20"><path d="M6.66675 5.83334L2.50008 10L6.66675 14.1667" fill="none" stroke-width="1.8" stroke-linecap="round" stroke-linejoin="round"></path><path d="M13.3333 14.1667L17.4999 9.99999L13.3333 5.83332" fill="none" stroke-width="1.8" stroke-linecap="round" stroke-linejoin="round"></path></svg></button>
                                    <button class="icon-button icon-button-single" aria-label="Cite"><svg viewBox="0 0 20 20"><path fill-rule="evenodd" clip-rule="evenodd" stroke="none" d="M5.89959 8.0001C5.89959 7.39258 6.39208 6.9001 6.99959 6.9001C7.60711 6.9001 8.09959 7.39258 8.09959 8.0001C8.09959 8.60761 7.60711 9.1001 6.99959 9.1001C6.39208 9.1001 5.89959 8.60761 5.89959 8.0001ZM6.99959 5.1001C5.39797 5.1001 4.09959 6.39847 4.09959 8.0001C4.09959 9.60172 5.39