<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Contact z.calculator.city - Get in touch with our team for support, feedback, or questions about our free online calculators.">
    <meta name="keywords" content="contact, support, feedback, calculator help, z.calculator.city contact">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="Contact Us | z.calculator.city">
    <meta property="og:description" content="Contact z.calculator.city - Get in touch with our team for support, feedback, or questions about our free online calculators.">
    <meta property="og:site_name" content="z.calculator.city">
    <meta property="og:url" content="https://z.calculator.city/contact">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://z.calculator.city/contact">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <title>Contact Us | z.calculator.city</title>

    <!-- Google AdSense -->
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2205593928173688"
         crossorigin="anonymous"></script>

    <style>
        /* General Body and HTML styles */
        html { -webkit-font-smoothing: antialiased; box-sizing: border-box; text-size-adjust: 100%; scroll-padding-top: 119px; }
        *, ::before, ::after { box-sizing: inherit; }
        body { margin: 0px; color: rgb(10, 10, 10); font-family: Verdana, sans-serif; font-size: 15px; font-weight: 400; line-height: 141%; letter-spacing: 0px; background-color: rgb(247, 249, 255); background-position: center 0px; background-repeat: no-repeat; overflow-y: scroll; }
        strong, b { font-weight: 700; }

        /* Header Styles */
        .app-header { z-index: 1202; top: 0px; background: rgb(255, 255, 255); width: 100%; box-shadow: rgba(59, 104, 252, 0.1) 0px 6px 16px 0px, rgba(41, 76, 194, 0.06) 0px 1px 2.8px 1px; padding: 8px 16px; position: fixed; left: 0px; right: 0px; }
        .header-container { max-width: 1920px; margin-inline: auto; display: flex; align-items: center; width: 100%; }
        .logo-section { position: relative; flex-grow: 1; display: flex; align-items: center; justify-content: space-between; padding-right: 16px; }
        .logo-link { text-decoration: none; display: flex; align-items: center; min-height: 40px; border-radius: 8px; color: #3B68FC; font-size: 24px; font-weight: bold; }
        .search-section { display: flex; align-items: center; flex-grow: 1; padding-left: 0; }
        .nav-section { align-items: center; width: 100%; display: none; }
        .nav-list { display: flex; flex-direction: column; gap: 16px; list-style-type: none; margin: 0; padding: 0; }
        .nav-link { text-decoration: none; color: rgb(33, 36, 39); }
        .nav-link-box { padding: 0px; position: relative; color: rgb(33, 36, 39); }
        .nav-link-text { padding: 0px; margin: 0px; color: inherit; text-align: left; font-size: 13.4px; font-style: normal; font-weight: 500; line-height: 20px; }

        /* Main Content Styles */
        .main-container { box-sizing: border-box; margin-inline: auto; padding-bottom: 40px; padding-top: 130px; max-width: 800px; padding-left: 20px; padding-right: 20px; }
        .content-section { background: white; border-radius: 12px; padding: 40px; margin-bottom: 40px; }
        .page-title { font-size: 36px; font-weight: 700; color: rgb(33, 36, 39); margin-bottom: 20px; text-align: center; }
        .page-subtitle { text-align: center; color: rgb(75, 82, 89); margin-bottom: 40px; font-size: 18px; }
        .contact-grid { display: grid; grid-template-columns: 1fr; gap: 30px; margin-bottom: 40px; }
        .contact-card { background: rgb(247, 249, 255); padding: 30px; border-radius: 12px; text-align: center; }
        .contact-icon { font-size: 48px; margin-bottom: 20px; }
        .contact-title { font-size: 20px; font-weight: 600; color: rgb(33, 36, 39); margin-bottom: 15px; }
        .contact-description { color: rgb(75, 82, 89); margin-bottom: 20px; line-height: 1.6; }
        .contact-link { color: #3B68FC; text-decoration: none; font-weight: 500; font-size: 16px; }
        .contact-link:hover { color: #294CC2; text-decoration: underline; }
        .form-section { margin-top: 40px; }
        .form-title { font-size: 24px; font-weight: 600; color: rgb(33, 36, 39); margin-bottom: 20px; text-align: center; }
        .contact-form { display: flex; flex-direction: column; gap: 20px; }
        .form-group { display: flex; flex-direction: column; }
        .form-label { font-weight: 500; color: rgb(33, 36, 39); margin-bottom: 8px; font-size: 14px; }
        .form-input, .form-textarea { padding: 12px 16px; border: 1px solid rgb(210, 220, 255); border-radius: 8px; font-family: Verdana, sans-serif; font-size: 14px; background-color: rgb(248, 249, 251); transition: border-color 0.3s ease; }
        .form-input:focus, .form-textarea:focus { outline: none; border-color: #3B68FC; }
        .form-textarea { min-height: 120px; resize: vertical; }
        .form-button { background-color: #3B68FC; color: white; border: none; padding: 16px 32px; border-radius: 8px; font-size: 16px; font-weight: 600; cursor: pointer; transition: background-color 0.3s ease; }
        .form-button:hover { background-color: #294CC2; }
        .social-section { margin-top: 40px; text-align: center; }
        .social-title { font-size: 20px; font-weight: 600; color: rgb(33, 36, 39); margin-bottom: 20px; }
        .social-links { display: flex; justify-content: center; gap: 20px; }
        .social-link { display: inline-flex; align-items: center; justify-content: center; width: 50px; height: 50px; background: #3B68FC; border-radius: 50%; color: white; text-decoration: none; transition: background-color 0.3s ease; }
        .social-link:hover { background-color: #294CC2; }
        .social-link svg { width: 24px; height: 24px; fill: white; }

        /* Footer Styles */
        .footer { background: rgb(255, 255, 255); padding-bottom: 0px; margin-top: 60px; }
        .footer-container { margin-inline: auto; max-width: 1920px; }
        .footer-base { padding: 24px; display: flex; gap: 24px; align-items: center; justify-content: center; flex-direction: column; border-top: 1px solid rgb(223, 226, 235); }
        .footer-copyright { padding: 0px; margin: 0px; color: rgb(116, 128, 145); text-align: left; font-size: 13.4px; font-style: normal; font-weight: 600; line-height: 24px; }
        .footer-base-link { color: rgb(116, 128, 145); transition: color 200ms linear; text-decoration: none; font-size: 13.4px; font-weight: 600; line-height: 24px; }
        .footer-base-link:hover { color: rgb(59, 104, 252); }

        /* Responsive Media Queries */
        @media (min-width: 584px) {
            .app-header { padding: 16px 24px; }
            .logo-section { flex-grow: 0; padding-right: 0; }
            .search-section { padding-left: 16px; }
            .main-container { padding-top: 58px; }
            .footer-base { flex-direction: row; }
            .contact-grid { grid-template-columns: repeat(2, 1fr); }
        }
        @media (min-width: 1146px) {
            .main-container { padding-top: 113px; }
            .header-container { flex-direction: column; }
            .nav-section { display: block; padding-top: 16px; }
            .nav-list { flex-direction: row; justify-content: space-between; gap: 0; }
            .nav-link-box::after { display: block; position: absolute; content: ""; inset: -8px 0 -16px; border-bottom: 3px solid transparent; }
            .nav-link-box:hover { color: rgb(59, 104, 252); }
            .contact-grid { grid-template-columns: repeat(3, 1fr); }
        }
    </style>
</head>
<body>
    <div id="__next">
        <header class="app-header">
            <div class="header-container">
                <div class="header-top-row" style="display: flex; align-items: center; width: 100%;">
                    <div class="logo-section">
                        <a class="logo-link" href="https://z.calculator.city">
                            z.calculator.city
                        </a>
                    </div>
                    <div class="search-section">
                        <!-- Search bar can be implemented here -->
                    </div>
                </div>
                <nav class="nav-section">
                    <ul class="nav-list">
                        <li><a class="nav-link" href="/biology-calculators"><div class="nav-link-box"><span class="nav-link-text">Biology</span></div></a></li>
                        <li><a class="nav-link" href="/chemistry-calculators"><div class="nav-link-box"><span class="nav-link-text">Chemistry</span></div></a></li>
                        <li><a class="nav-link" href="/math-calculators"><div class="nav-link-box"><span class="nav-link-text">Math</span></div></a></li>
                        <li><a class="nav-link" href="/physics-calculators"><div class="nav-link-box"><span class="nav-link-text">Physics</span></div></a></li>
                        <li><a class="nav-link" href="/health-calculators"><div class="nav-link-box"><span class="nav-link-text">Health</span></div></a></li>
                        <li><a class="nav-link" href="/finance-calculators"><div class="nav-link-box"><span class="nav-link-text">Finance</span></div></a></li>
                    </ul>
                </nav>
            </div>
        </header>

        <main class="main-container">
            <div class="content-section">
                <h1 class="page-title">Contact Us</h1>
                <p class="page-subtitle">We'd love to hear from you! Get in touch with our team for support, feedback, or any questions about our calculators.</p>

                <div class="contact-grid">
                    <div class="contact-card">
                        <div class="contact-icon">📧</div>
                        <h3 class="contact-title">Email Support</h3>
                        <p class="contact-description">Send us an email and we'll respond within 24 hours.</p>
                        <a href="mailto:<EMAIL>" class="contact-link"><EMAIL></a>
                    </div>

                    <div class="contact-card">
                        <div class="contact-icon">🐛</div>
                        <h3 class="contact-title">Bug Reports</h3>
                        <p class="contact-description">Found a bug or error in our calculators? Let us know!</p>
                        <a href="mailto:<EMAIL>" class="contact-link"><EMAIL></a>
                    </div>

                    <div class="contact-card">
                        <div class="contact-icon">💡</div>
                        <h3 class="contact-title">Feature Requests</h3>
                        <p class="contact-description">Have an idea for a new calculator or feature?</p>
                        <a href="mailto:<EMAIL>" class="contact-link"><EMAIL></a>
                    </div>
                </div>

                <div class="form-section">
                    <h2 class="form-title">Send us a Message</h2>
                    <form class="contact-form" action="#" method="POST">
                        <div class="form-group">
                            <label for="name" class="form-label">Name *</label>
                            <input type="text" id="name" name="name" class="form-input" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="email" class="form-label">Email *</label>
                            <input type="email" id="email" name="email" class="form-input" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="subject" class="form-label">Subject *</label>
                            <input type="text" id="subject" name="subject" class="form-input" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="message" class="form-label">Message *</label>
                            <textarea id="message" name="message" class="form-textarea" required placeholder="Please describe your question, feedback, or issue in detail..."></textarea>
                        </div>
                        
                        <button type="submit" class="form-button">Send Message</button>
                    </form>
                </div>

                <div class="social-section">
                    <h3 class="social-title">Follow Us</h3>
                    <div class="social-links">
                        <a href="https://facebook.com/zcalculatorcity" class="social-link" aria-label="Facebook">
                            <svg viewBox="0 0 24 24"><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg>
                        </a>
                        <a href="https://twitter.com/zcalculatorcity" class="social-link" aria-label="Twitter">
                            <svg viewBox="0 0 24 24"><path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/></svg>
                        </a>
                        <a href="https://instagram.com/zcalculatorcity" class="social-link" aria-label="Instagram">
                            <svg viewBox="0 0 24 24"><path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/></svg>
                        </a>
                        <a href="https://linkedin.com/company/zcalculatorcity" class="social-link" aria-label="LinkedIn">
                            <svg viewBox="0 0 24 24"><path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/></svg>
                        </a>
                    </div>
                </div>

                <!-- Advertisement -->
                <div style="background: white; border-radius: 12px; padding: 30px; margin-top: 40px; text-align: center; box-shadow: rgba(59, 104, 252, 0.1) 0px 6px 16px 0px;">
                    <div style="font-size: 12px; color: rgb(116, 128, 145); margin-bottom: 15px; text-transform: uppercase; letter-spacing: 1px;">Advertisement</div>
                    <ins class="adsbygoogle"
                         style="display:block"
                         data-ad-client="ca-pub-2205593928173688"
                         data-ad-slot="1111111111"
                         data-ad-format="auto"
                         data-full-width-responsive="true"></ins>
                    <script>
                         (adsbygoogle = window.adsbygoogle || []).push({});
                    </script>
                </div>
            </div>
        </main>

        <footer class="footer">
            <div class="footer-container">
                <div class="footer-base">
                    <span class="footer-copyright">© 2024 z.calculator.city. All rights reserved.</span>
                    <div style="display: flex; gap: 16px;">
                        <a href="/privacy-policy" class="footer-base-link">Privacy Policy</a>
                        <a href="/terms-of-service" class="footer-base-link">Terms of Service</a>
                        <a href="/contact" class="footer-base-link">Contact</a>
                    </div>
                </div>
            </div>
        </footer>
    </div>
</body>
</html>
