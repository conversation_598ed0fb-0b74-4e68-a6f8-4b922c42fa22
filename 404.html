<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Page not found - The page you're looking for doesn't exist. Explore our free online calculators for biology, chemistry, physics, math, health, and finance.">
    <meta name="robots" content="noindex, follow">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="Page Not Found | y.calculator.city">
    <meta property="og:description" content="The page you're looking for doesn't exist. Explore our free online calculators instead.">
    <meta property="og:site_name" content="y.calculator.city">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <title>Page Not Found | y.calculator.city</title>
    
    <style>
        /* General Body and HTML styles */
        html { -webkit-font-smoothing: antialiased; box-sizing: border-box; text-size-adjust: 100%; scroll-padding-top: 119px; }
        *, ::before, ::after { box-sizing: inherit; }
        body { margin: 0px; color: rgb(10, 10, 10); font-family: Verdana, sans-serif; font-size: 15px; font-weight: 400; line-height: 141%; letter-spacing: 0px; background-color: rgb(247, 249, 255); background-position: center 0px; background-repeat: no-repeat; overflow-y: scroll; }
        strong, b { font-weight: 700; }

        /* Header Styles */
        .app-header { z-index: 1202; top: 0px; background: rgb(255, 255, 255); width: 100%; box-shadow: rgba(59, 104, 252, 0.1) 0px 6px 16px 0px, rgba(41, 76, 194, 0.06) 0px 1px 2.8px 1px; padding: 8px 16px; position: fixed; left: 0px; right: 0px; }
        .header-container { max-width: 1920px; margin-inline: auto; display: flex; align-items: center; width: 100%; }
        .logo-section { position: relative; flex-grow: 1; display: flex; align-items: center; justify-content: space-between; padding-right: 16px; }
        .logo-link { text-decoration: none; display: flex; align-items: center; min-height: 40px; border-radius: 8px; color: #3B68FC; font-size: 24px; font-weight: bold; }
        .search-section { display: flex; align-items: center; flex-grow: 1; padding-left: 0; }
        .nav-section { align-items: center; width: 100%; display: none; }
        .nav-list { display: flex; flex-direction: column; gap: 16px; list-style-type: none; margin: 0; padding: 0; }
        .nav-link { text-decoration: none; color: rgb(33, 36, 39); }
        .nav-link-box { padding: 0px; position: relative; color: rgb(33, 36, 39); }
        .nav-link-text { padding: 0px; margin: 0px; color: inherit; text-align: left; font-size: 13.4px; font-style: normal; font-weight: 500; line-height: 20px; }

        /* Main Content Styles */
        .main-container { box-sizing: border-box; margin-inline: auto; padding-bottom: 40px; padding-top: 130px; max-width: 800px; padding-left: 20px; padding-right: 20px; min-height: 70vh; display: flex; align-items: center; justify-content: center; }
        .error-section { background: white; border-radius: 12px; padding: 60px 40px; text-align: center; }
        .error-code { font-size: 120px; font-weight: 700; color: #3B68FC; margin-bottom: 20px; line-height: 1; }
        .error-title { font-size: 36px; font-weight: 700; color: rgb(33, 36, 39); margin-bottom: 20px; }
        .error-description { color: rgb(75, 82, 89); font-size: 18px; line-height: 1.6; margin-bottom: 40px; }
        .action-buttons { display: flex; flex-direction: column; gap: 20px; align-items: center; }
        .primary-button { background-color: #3B68FC; color: white; border: none; padding: 16px 32px; border-radius: 8px; font-size: 16px; font-weight: 600; text-decoration: none; display: inline-block; transition: background-color 0.3s ease; }
        .primary-button:hover { background-color: #294CC2; }
        .secondary-button { background-color: white; color: #3B68FC; border: 2px solid #3B68FC; padding: 14px 30px; border-radius: 8px; font-size: 16px; font-weight: 600; text-decoration: none; display: inline-block; transition: all 0.3s ease; }
        .secondary-button:hover { background-color: #3B68FC; color: white; }
        .suggestions-section { margin-top: 50px; }
        .suggestions-title { font-size: 24px; font-weight: 600; color: rgb(33, 36, 39); margin-bottom: 30px; }
        .suggestions-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; }
        .suggestion-card { background: rgb(247, 249, 255); padding: 20px; border-radius: 8px; text-align: center; }
        .suggestion-icon { font-size: 32px; margin-bottom: 15px; }
        .suggestion-title { font-size: 16px; font-weight: 600; color: rgb(33, 36, 39); margin-bottom: 10px; }
        .suggestion-link { color: #3B68FC; text-decoration: none; font-weight: 500; }
        .suggestion-link:hover { text-decoration: underline; }

        /* Footer Styles */
        .footer { background: rgb(255, 255, 255); padding-bottom: 0px; margin-top: 60px; }
        .footer-container { margin-inline: auto; max-width: 1920px; }
        .footer-base { padding: 24px; display: flex; gap: 24px; align-items: center; justify-content: center; flex-direction: column; border-top: 1px solid rgb(223, 226, 235); }
        .footer-copyright { padding: 0px; margin: 0px; color: rgb(116, 128, 145); text-align: left; font-size: 13.4px; font-style: normal; font-weight: 600; line-height: 24px; }
        .footer-base-link { color: rgb(116, 128, 145); transition: color 200ms linear; text-decoration: none; font-size: 13.4px; font-weight: 600; line-height: 24px; }
        .footer-base-link:hover { color: rgb(59, 104, 252); }

        /* Responsive Media Queries */
        @media (max-width: 768px) {
            .error-code { font-size: 80px; }
            .error-title { font-size: 28px; }
            .error-description { font-size: 16px; }
            .action-buttons { flex-direction: column; }
            .suggestions-grid { grid-template-columns: 1fr; }
        }
        @media (min-width: 584px) {
            .app-header { padding: 16px 24px; }
            .logo-section { flex-grow: 0; padding-right: 0; }
            .search-section { padding-left: 16px; }
            .main-container { padding-top: 58px; }
            .footer-base { flex-direction: row; }
        }
        @media (min-width: 1146px) {
            .main-container { padding-top: 113px; }
            .header-container { flex-direction: column; }
            .nav-section { display: block; padding-top: 16px; }
            .nav-list { flex-direction: row; justify-content: space-between; gap: 0; }
            .nav-link-box::after { display: block; position: absolute; content: ""; inset: -8px 0 -16px; border-bottom: 3px solid transparent; }
            .nav-link-box:hover { color: rgb(59, 104, 252); }
        }
    </style>
</head>
<body>
    <div id="__next">
        <header class="app-header">
            <div class="header-container">
                <div class="header-top-row" style="display: flex; align-items: center; width: 100%;">
                    <div class="logo-section">
                        <a class="logo-link" href="/">
                            y.calculator.city
                        </a>
                    </div>
                    <div class="search-section">
                        <!-- Search bar can be implemented here -->
                    </div>
                </div>
                <nav class="nav-section">
                    <ul class="nav-list">
                        <li><a class="nav-link" href="/biology-calculators"><div class="nav-link-box"><span class="nav-link-text">Biology</span></div></a></li>
                        <li><a class="nav-link" href="/chemistry-calculators"><div class="nav-link-box"><span class="nav-link-text">Chemistry</span></div></a></li>
                        <li><a class="nav-link" href="/math-calculators"><div class="nav-link-box"><span class="nav-link-text">Math</span></div></a></li>
                        <li><a class="nav-link" href="/physics-calculators"><div class="nav-link-box"><span class="nav-link-text">Physics</span></div></a></li>
                        <li><a class="nav-link" href="/health-calculators"><div class="nav-link-box"><span class="nav-link-text">Health</span></div></a></li>
                        <li><a class="nav-link" href="/finance-calculators"><div class="nav-link-box"><span class="nav-link-text">Finance</span></div></a></li>
                    </ul>
                </nav>
            </div>
        </header>

        <main class="main-container">
            <div class="error-section">
                <div class="error-code">404</div>
                <h1 class="error-title">Page Not Found</h1>
                <p class="error-description">
                    Oops! The page you're looking for doesn't exist. It might have been moved, deleted, or you entered the wrong URL.
                </p>
                
                <div class="action-buttons">
                    <a href="/" class="primary-button">Go to Homepage</a>
                    <button onclick="history.back()" class="secondary-button">Go Back</button>
                </div>

                <div class="suggestions-section">
                    <h2 class="suggestions-title">Popular Calculators</h2>
                    <div class="suggestions-grid">
                        <div class="suggestion-card">
                            <div class="suggestion-icon">🧬</div>
                            <h3 class="suggestion-title">Biology</h3>
                            <a href="/biological-age-calculator" class="suggestion-link">Biological Age Calculator</a>
                        </div>
                        <div class="suggestion-card">
                            <div class="suggestion-icon">⚗️</div>
                            <h3 class="suggestion-title">Chemistry</h3>
                            <a href="/molarity-calculator" class="suggestion-link">Molarity Calculator</a>
                        </div>
                        <div class="suggestion-card">
                            <div class="suggestion-icon">❤️</div>
                            <h3 class="suggestion-title">Health</h3>
                            <a href="/bmi-calculator" class="suggestion-link">BMI Calculator</a>
                        </div>
                        <div class="suggestion-card">
                            <div class="suggestion-icon">💰</div>
                            <h3 class="suggestion-title">Finance</h3>
                            <a href="/loan-calculator" class="suggestion-link">Loan Calculator</a>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <footer class="footer">
            <div class="footer-container">
                <div class="footer-base">
                    <span class="footer-copyright">© 2024 y.calculator.city. All rights reserved.</span>
                    <div style="display: flex; gap: 16px;">
                        <a href="/privacy-policy" class="footer-base-link">Privacy Policy</a>
                        <a href="/terms-of-service" class="footer-base-link">Terms of Service</a>
                        <a href="/contact" class="footer-base-link">Contact</a>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // Track 404 errors for analytics (when implemented)
        if (typeof gtag !== 'undefined') {
            gtag('event', 'page_view', {
                page_title: '404 - Page Not Found',
                page_location: window.location.href
            });
        }
    </script>
</body>
</html>
