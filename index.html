<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Free online calculators for biology, chemistry, physics, math, health, and finance. Calculate biological age, AP biology scores, molecular biology calculations, and more with y.calculator.city.">
    <meta name="keywords" content="calculator, biology calculator, biological age calculator, AP biology calculator, chemistry calculator, physics calculator, math calculator, health calculator, finance calculator, free online calculator">
    <meta name="author" content="y.calculator.city">
    <meta name="robots" content="index, follow">
    <meta name="googlebot" content="index, follow">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="Free Online Calculators - Biology, Chemistry, Physics & More | y.calculator.city">
    <meta property="og:description" content="Free online calculators for biology, chemistry, physics, math, health, and finance. Calculate biological age, AP biology scores, molecular biology calculations, and more.">
    <meta property="og:site_name" content="y.calculator.city">
    <meta property="og:url" content="https://y.calculator.city">
    <meta property="og:image" content="https://y.calculator.city/images/og-image.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:locale" content="en_US">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@ycalculatorcity">
    <meta name="twitter:title" content="Free Online Calculators - Biology, Chemistry, Physics & More">
    <meta name="twitter:description" content="Free online calculators for biology, chemistry, physics, math, health, and finance. Calculate biological age, AP biology scores, molecular biology calculations, and more.">
    <meta name="twitter:image" content="https://y.calculator.city/images/twitter-card.jpg">

    <!-- Canonical URL -->
    <link rel="canonical" href="https://y.calculator.city">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "y.calculator.city",
        "url": "https://y.calculator.city",
        "description": "Free online calculators for biology, chemistry, physics, math, health, and finance",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "https://y.calculator.city/search?q={search_term_string}",
            "query-input": "required name=search_term_string"
        },
        "publisher": {
            "@type": "Organization",
            "name": "y.calculator.city",
            "url": "https://y.calculator.city",
            "logo": {
                "@type": "ImageObject",
                "url": "https://y.calculator.city/images/logo.png"
            }
        }
    }
    </script>

    <title>Free Online Calculators - Biology, Chemistry, Physics & More | y.calculator.city</title>

    <!-- Google AdSense -->
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2205593928173688"
         crossorigin="anonymous"></script>

    <style>
        /* All CSS from the original MHTML file is consolidated here for 100% replication of style */
        /* General Body and HTML styles */
        html { -webkit-font-smoothing: antialiased; box-sizing: border-box; text-size-adjust: 100%; scroll-padding-top: 119px; }
        *, ::before, ::after { box-sizing: inherit; }
        body { margin: 0px; color: rgb(10, 10, 10); font-family: Verdana, sans-serif; font-size: 15px; font-weight: 400; line-height: 141%; letter-spacing: 0px; background-color: rgb(247, 249, 255); background-position: center 0px; background-repeat: no-repeat; overflow-y: scroll; }
        strong, b { font-weight: 700; }

        /* Header Styles */
        .app-header { z-index: 1202; top: 0px; background: rgb(255, 255, 255); width: 100%; box-shadow: rgba(59, 104, 252, 0.1) 0px 6px 16px 0px, rgba(41, 76, 194, 0.06) 0px 1px 2.8px 1px; padding: 8px 16px; position: fixed; left: 0px; right: 0px; }
        .header-container { max-width: 1920px; margin-inline: auto; display: flex; align-items: center; width: 100%; }
        .logo-section { position: relative; flex-grow: 1; display: flex; align-items: center; justify-content: space-between; padding-right: 16px; }
        .logo-link { text-decoration: none; display: flex; align-items: center; min-height: 40px; border-radius: 8px; color: #3B68FC; font-size: 24px; font-weight: bold; }
        .search-section { display: flex; align-items: center; flex-grow: 1; padding-left: 0; }
        .nav-section { align-items: center; width: 100%; display: none; }
        .nav-list { display: flex; flex-direction: column; gap: 16px; list-style-type: none; margin: 0; padding: 0; }
        .nav-link { text-decoration: none; color: rgb(33, 36, 39); }
        .nav-link-box { padding: 0px; position: relative; color: rgb(33, 36, 39); }
        .nav-link-text { padding: 0px; margin: 0px; color: inherit; text-align: left; font-size: 13.4px; font-style: normal; font-weight: 500; line-height: 20px; }

        /* Main Content Styles */
        .main-container { box-sizing: border-box; margin-inline: auto; padding-bottom: 40px; padding-top: 130px; max-width: 1200px; padding-left: 20px; padding-right: 20px; display: grid; grid-template-columns: 1fr 300px; gap: 40px; }
        .content-area { min-width: 0; }
        .sidebar { display: flex; flex-direction: column; gap: 30px; }
        .ad-container { background: white; border-radius: 12px; padding: 20px; box-shadow: rgba(59, 104, 252, 0.1) 0px 6px 16px 0px; text-align: center; }
        .ad-label { font-size: 12px; color: rgb(116, 128, 145); margin-bottom: 15px; text-transform: uppercase; letter-spacing: 1px; }
        .sidebar-widget { background: white; border-radius: 12px; padding: 30px; box-shadow: rgba(59, 104, 252, 0.1) 0px 6px 16px 0px; }
        .widget-title { font-size: 20px; font-weight: 600; color: rgb(33, 36, 39); margin-bottom: 20px; }
        .widget-list { list-style: none; padding: 0; margin: 0; }
        .widget-list li { margin-bottom: 12px; }
        .widget-link { color: #3B68FC; text-decoration: none; font-weight: 500; transition: color 0.3s ease; }
        .widget-link:hover { color: #294CC2; }
        .hero-section { text-align: center; margin-bottom: 60px; }
        .hero-title { font-size: 48px; font-weight: 700; color: rgb(33, 36, 39); margin-bottom: 20px; line-height: 1.2; }
        .hero-subtitle { font-size: 20px; color: rgb(75, 82, 89); margin-bottom: 40px; max-width: 800px; margin-left: auto; margin-right: auto; }
        .categories-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px; margin-bottom: 60px; }
        .category-card { background: white; border-radius: 12px; padding: 30px; box-shadow: rgba(59, 104, 252, 0.1) 0px 6px 16px 0px; transition: transform 0.3s ease, box-shadow 0.3s ease; }
        .category-card:hover { transform: translateY(-5px); box-shadow: rgba(59, 104, 252, 0.2) 0px 12px 24px 0px; }
        .category-icon { font-size: 48px; margin-bottom: 20px; }
        .category-title { font-size: 24px; font-weight: 600; color: rgb(33, 36, 39); margin-bottom: 15px; }
        .category-description { color: rgb(75, 82, 89); margin-bottom: 20px; line-height: 1.6; }
        .category-links { display: flex; flex-direction: column; gap: 8px; }
        .category-link { color: #3B68FC; text-decoration: none; font-weight: 500; transition: color 0.3s ease; }
        .category-link:hover { color: #294CC2; }
        .features-section { background: white; border-radius: 12px; padding: 40px; margin-bottom: 60px; }
        .features-title { font-size: 32px; font-weight: 600; text-align: center; margin-bottom: 40px; color: rgb(33, 36, 39); }
        .features-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 30px; }
        .feature-item { text-align: center; }
        .feature-icon { font-size: 36px; margin-bottom: 15px; color: #3B68FC; }
        .feature-title { font-size: 18px; font-weight: 600; margin-bottom: 10px; color: rgb(33, 36, 39); }
        .feature-description { color: rgb(75, 82, 89); line-height: 1.6; }

        /* Footer Styles */
        .footer { background: rgb(255, 255, 255); padding-bottom: 0px; }
        .footer-container { margin-inline: auto; max-width: 1920px; }
        .footer-main { border-bottom: 1px solid rgb(223, 226, 235); padding-bottom: 32px; display: flex; flex-direction: column-reverse; }
        .footer-logo-section { display: flex; flex-direction: column-reverse; gap: 32px; }
        .footer-links-section { display: grid; grid-template-columns: 1fr; gap: 24px; }
        .footer-category-section { display: flex; align-items: center; flex-direction: column; gap: 32px; }
        .footer-category-title { padding: 0px; margin: 0px; color: rgb(33, 36, 39); text-align: left; font-size: 15.8px; font-style: normal; font-weight: 700; line-height: 24px; }
        .footer-category-list { display: grid; grid-template-columns: repeat(2, 1fr); width: 100%; gap: 16px 24px; }
        .footer-base { padding: 24px; display: flex; gap: 24px; align-items: center; justify-content: center; flex-direction: column; }
        .footer-copyright { padding: 0px; margin: 0px; color: rgb(116, 128, 145); text-align: left; font-size: 13.4px; font-style: normal; font-weight: 600; line-height: 24px; }
        .footer-social-links { display: flex; justify-content: center; gap: 12px; }
        .social-link { padding: 8px; }
        .social-link svg { width: 24px; height: 24px; fill: rgb(75, 82, 89); transition: fill 100ms linear; }
        .social-link:hover svg { fill: rgb(41, 76, 194); }
        .footer-graphic-section { position: relative; }
        .footer-graphic-text { display: flex; flex-direction: column; margin: auto; max-width: 200px; }
        .footer-graphic-title { padding: 0px; margin: 0px; color: rgb(33, 36, 39); text-align: left; font-size: 28px; font-style: normal; font-weight: 700; line-height: 36px; }
        .footer-logo-img { display: block; }
        .footer-base-link { color: rgb(116, 128, 145); transition: color 200ms linear; text-decoration: none; font-size: 13.4px; font-weight: 600; line-height: 24px; }
        .footer-base-link:hover { color: rgb(59, 104, 252); }

        /* Responsive Media Queries */
        @media (max-width: 1024px) {
            .main-container { grid-template-columns: 1fr; gap: 30px; }
            .sidebar { order: -1; }
            .ad-container { max-width: 400px; margin: 0 auto; }
        }
        @media (max-width: 768px) {
            .sidebar { display: none; }
            .main-container { grid-template-columns: 1fr; }
        }
        @media (min-width: 584px) {
            .app-header { padding: 16px 24px; }
            .logo-section { flex-grow: 0; padding-right: 0; }
            .search-section { padding-left: 16px; }
            .main-container { padding-top: 58px; padding-right: 16px; }
            .footer-main { padding-bottom: 24px; }
            .footer-logo-section { border-top: 1px solid rgb(223, 226, 235); padding-top: 32px; gap: 24px; }
            .footer-links-section { grid-template-columns: 1fr 1fr 1fr; gap: 32px; }
            .footer-category-section { align-items: flex-start; }
            .footer-category-list { grid-template-columns: repeat(3, 1fr); gap: 16px 32px; }
            .footer-base { flex-direction: row; }
        }
        @media (min-width: 768px) {
            .hero-title { font-size: 56px; }
            .categories-grid { grid-template-columns: repeat(2, 1fr); }
        }
        @media (min-width: 1146px) {
            .main-container { padding-top: 113px; }
            .header-container { flex-direction: column; }
            .nav-section { display: block; padding-top: 16px; }
            .nav-list { flex-direction: row; justify-content: space-between; gap: 0; }
            .nav-link-box::after { display: block; position: absolute; content: ""; inset: -8px 0 -16px; border-bottom: 3px solid transparent; }
            .nav-link-box:hover { color: rgb(59, 104, 252); }
            .footer-main { display: grid; grid-template-columns: 352px 1fr; padding-bottom: 0px; }
            .footer-logo-section { flex-direction: column; border-top: 0px; padding-top: 0px; gap: 8px; }
            .footer-graphic-text { gap: 28px; padding-right: 20px; padding-bottom: 36px; }
            .categories-grid { grid-template-columns: repeat(3, 1fr); }
        }
    </style>
</head>
<body>
    <div id="__next">
        <header class="app-header">
            <div class="header-container">
                <div class="header-top-row" style="display: flex; align-items: center; width: 100%;">
                    <div class="logo-section">
                        <a class="logo-link" href="https://y.calculator.city">
                            y.calculator.city
                        </a>
                    </div>
                    <div class="search-section">
                        <!-- Search bar can be implemented here -->
                    </div>
                </div>
                <nav class="nav-section">
                    <ul class="nav-list">
                        <li><a class="nav-link" href="/biology-calculators"><div class="nav-link-box"><span class="nav-link-text">Biology</span></div></a></li>
                        <li><a class="nav-link" href="/chemistry-calculators"><div class="nav-link-box"><span class="nav-link-text">Chemistry</span></div></a></li>
                        <li><a class="nav-link" href="/math-calculators"><div class="nav-link-box"><span class="nav-link-text">Math</span></div></a></li>
                        <li><a class="nav-link" href="/physics-calculators"><div class="nav-link-box"><span class="nav-link-text">Physics</span></div></a></li>
                        <li><a class="nav-link" href="/health-calculators"><div class="nav-link-box"><span class="nav-link-text">Health</span></div></a></li>
                        <li><a class="nav-link" href="/finance-calculators"><div class="nav-link-box"><span class="nav-link-text">Finance</span></div></a></li>
                    </ul>
                </nav>
            </div>
        </header>

        <main class="main-container">
            <div class="content-area">
                <section class="hero-section">
                    <h1 class="hero-title">Free Online Calculators</h1>
                    <p class="hero-subtitle">Powerful, accurate, and easy-to-use calculators for biology, chemistry, physics, math, health, and finance. Get instant results with our comprehensive collection of scientific and educational tools.</p>
                </section>

            <section class="categories-grid">
                <div class="category-card">
                    <div class="category-icon">🧬</div>
                    <h2 class="category-title">Biology Calculators</h2>
                    <p class="category-description">Calculate biological age, AP biology scores, molecular biology calculations, and more with our comprehensive biology tools.</p>
                    <div class="category-links">
                        <a href="/biological-age-calculator" class="category-link">Biological Age Calculator</a>
                        <a href="/ap-biology-calculator" class="category-link">AP Biology Score Calculator</a>
                        <a href="/biology-calculator" class="category-link">General Biology Calculator</a>
                        <a href="/bbc-biological-age-calculator" class="category-link">BBC Biological Age Calculator</a>
                    </div>
                </div>

                <div class="category-card">
                    <div class="category-icon">⚗️</div>
                    <h2 class="category-title">Chemistry Calculators</h2>
                    <p class="category-description">Solve chemical equations, calculate molarity, molecular weights, and perform various chemistry calculations.</p>
                    <div class="category-links">
                        <a href="/molarity-calculator" class="category-link">Molarity Calculator</a>
                        <a href="/molecular-weight-calculator" class="category-link">Molecular Weight Calculator</a>
                        <a href="/ph-calculator" class="category-link">pH Calculator</a>
                        <a href="/stoichiometry-calculator" class="category-link">Stoichiometry Calculator</a>
                    </div>
                </div>

                <div class="category-card">
                    <div class="category-icon">🔢</div>
                    <h2 class="category-title">Math Calculators</h2>
                    <p class="category-description">From basic arithmetic to advanced calculus, solve mathematical problems with our comprehensive math tools.</p>
                    <div class="category-links">
                        <a href="/algebra-calculator" class="category-link">Algebra Calculator</a>
                        <a href="/calculus-calculator" class="category-link">Calculus Calculator</a>
                        <a href="/statistics-calculator" class="category-link">Statistics Calculator</a>
                        <a href="/geometry-calculator" class="category-link">Geometry Calculator</a>
                    </div>
                </div>
            </section>

            <section class="categories-grid">
                <div class="category-card">
                    <div class="category-icon">⚡</div>
                    <h2 class="category-title">Physics Calculators</h2>
                    <p class="category-description">Calculate force, energy, motion, and other physics concepts with our accurate physics calculation tools.</p>
                    <div class="category-links">
                        <a href="/force-calculator" class="category-link">Force Calculator</a>
                        <a href="/energy-calculator" class="category-link">Energy Calculator</a>
                        <a href="/motion-calculator" class="category-link">Motion Calculator</a>
                        <a href="/wave-calculator" class="category-link">Wave Calculator</a>
                    </div>
                </div>

                <div class="category-card">
                    <div class="category-icon">❤️</div>
                    <h2 class="category-title">Health Calculators</h2>
                    <p class="category-description">Monitor your health with BMI, BMR, calorie, and other health-related calculators for better wellness.</p>
                    <div class="category-links">
                        <a href="/bmi-calculator" class="category-link">BMI Calculator</a>
                        <a href="/bmr-calculator" class="category-link">BMR Calculator</a>
                        <a href="/calorie-calculator" class="category-link">Calorie Calculator</a>
                        <a href="/heart-rate-calculator" class="category-link">Heart Rate Calculator</a>
                    </div>
                </div>

                <div class="category-card">
                    <div class="category-icon">💰</div>
                    <h2 class="category-title">Finance Calculators</h2>
                    <p class="category-description">Plan your finances with loan, mortgage, investment, and retirement calculators for smart money decisions.</p>
                    <div class="category-links">
                        <a href="/loan-calculator" class="category-link">Loan Calculator</a>
                        <a href="/mortgage-calculator" class="category-link">Mortgage Calculator</a>
                        <a href="/investment-calculator" class="category-link">Investment Calculator</a>
                        <a href="/retirement-calculator" class="category-link">Retirement Calculator</a>
                    </div>
                </div>
            </section>

            <section class="features-section">
                <h2 class="features-title">Why Choose z.calculator.city?</h2>
                <div class="features-grid">
                    <div class="feature-item">
                        <div class="feature-icon">🆓</div>
                        <h3 class="feature-title">100% Free</h3>
                        <p class="feature-description">All our calculators are completely free to use with no hidden fees or subscriptions required.</p>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">⚡</div>
                        <h3 class="feature-title">Instant Results</h3>
                        <p class="feature-description">Get accurate calculations instantly without waiting or complex setup procedures.</p>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">🎯</div>
                        <h3 class="feature-title">Accurate & Reliable</h3>
                        <p class="feature-description">Our calculators use scientifically proven formulas and are regularly updated for accuracy.</p>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">📱</div>
                        <h3 class="feature-title">Mobile Friendly</h3>
                        <p class="feature-description">Access all calculators on any device - desktop, tablet, or smartphone with responsive design.</p>
                    </div>
                </div>
            </section>
            </div>

            <!-- Sidebar with Ads -->
            <aside class="sidebar">
                <!-- Google AdSense Ad Unit -->
                <div class="ad-container">
                    <div class="ad-label">Advertisement</div>
                    <!-- AdSense Display Ad -->
                    <ins class="adsbygoogle"
                         style="display:block"
                         data-ad-client="ca-pub-2205593928173688"
                         data-ad-slot="1234567890"
                         data-ad-format="auto"
                         data-full-width-responsive="true"></ins>
                    <script>
                         (adsbygoogle = window.adsbygoogle || []).push({});
                    </script>
                </div>

                <!-- Popular Calculators Widget -->
                <div class="sidebar-widget">
                    <h3 class="widget-title">Popular Calculators</h3>
                    <ul class="widget-list">
                        <li><a href="/biological-age-calculator" class="widget-link">Biological Age Calculator</a></li>
                        <li><a href="/ap-biology-calculator" class="widget-link">AP Biology Calculator</a></li>
                        <li><a href="/bmi-calculator" class="widget-link">BMI Calculator</a></li>
                        <li><a href="/molarity-calculator" class="widget-link">Molarity Calculator</a></li>
                        <li><a href="/loan-calculator" class="widget-link">Loan Calculator</a></li>
                        <li><a href="/calorie-calculator" class="widget-link">Calorie Calculator</a></li>
                    </ul>
                </div>

                <!-- Categories Widget -->
                <div class="sidebar-widget">
                    <h3 class="widget-title">Calculator Categories</h3>
                    <ul class="widget-list">
                        <li><a href="/biology-calculators" class="widget-link">🧬 Biology Calculators</a></li>
                        <li><a href="/chemistry-calculators" class="widget-link">⚗️ Chemistry Calculators</a></li>
                        <li><a href="/math-calculators" class="widget-link">🔢 Math Calculators</a></li>
                        <li><a href="/physics-calculators" class="widget-link">⚡ Physics Calculators</a></li>
                        <li><a href="/health-calculators" class="widget-link">❤️ Health Calculators</a></li>
                        <li><a href="/finance-calculators" class="widget-link">💰 Finance Calculators</a></li>
                    </ul>
                </div>

                <!-- Second Ad Unit -->
                <div class="ad-container">
                    <div class="ad-label">Advertisement</div>
                    <!-- AdSense Display Ad -->
                    <ins class="adsbygoogle"
                         style="display:block"
                         data-ad-client="ca-pub-2205593928173688"
                         data-ad-slot="**********"
                         data-ad-format="auto"
                         data-full-width-responsive="true"></ins>
                    <script>
                         (adsbygoogle = window.adsbygoogle || []).push({});
                    </script>
                </div>
            </aside>
        </main>

        <footer class="footer">
            <div class="footer-container">
                <div class="footer-main">
                    <div class="footer-logo-section">
                        <div class="footer-graphic-section">
                            <div class="footer-graphic-text">
                                <span class="footer-graphic-title">We make it count!</span>
                                <img alt="z.calculator.city logo" loading="lazy" width="174" height="17" decoding="async" class="footer-logo-img" src="data:image/svg+xml,%3Csvg width='204' height='20' viewBox='0 0 204 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cg clip-path='url(%23clip0_2772_294411)'%3E%3Cpath d='M40.7692 7.88769C40.7692 5.99683 42.3615 4.46283 44.3241 4.46283C46.2868 4.46283 47.8791 5.99683 47.8791 7.88769C47.8791 7.91318 47.8839 15.5531 47.8839 18.5307C47.8839 19.1772 48.4275 19.6986 49.0985 19.6986H50.7798C51.4509 19.6986 51.9944 19.1749 51.9944 18.5284V7.88769C51.9944 3.80705 48.5622 0.498047 44.3266 0.498047C40.0909 0.498047 36.6562 3.80705 36.6562 7.88769C36.6562 7.91318 36.6611 15.5531 36.6611 18.5307C36.6611 19.1772 37.2046 19.6986 37.8757 19.6986H39.557C40.228 19.6986 40.7716 19.1749 40.7716 18.5284V7.88769H40.7692Z' fill='%233B68FC'/%3E%3Cpath d='M55.6016 8.70176C55.6016 8.72725 55.604 15.7044 55.604 18.5291C55.604 19.1756 56.1476 19.697 56.8186 19.697H58.4999C59.1709 19.697 59.7145 19.1733 59.7145 18.5268V8.69945C59.7145 6.25941 61.7686 4.4566 64.2989 4.4566C66.8292 4.4566 68.8809 6.25941 68.8809 8.69945C68.8809 8.72494 68.8833 15.7021 68.8857 18.5268C68.8857 19.1733 69.4293 19.6947 70.1003 19.6947H71.7816C72.4527 19.6947 72.9962 19.171 72.9962 18.5245V8.69713C72.9962 4.07194 69.1046 0.494141 64.3013 0.494141C59.498 0.494141 55.604 4.06962 55.604 8.69713L55.6016 8.70176Z' fill='%233B68FC'/%3E%3Cpath d='M36.6519 7.88903C36.6519 5.99817 35.0597 4.46417 33.097 4.46417C31.1343 4.46417 29.5372 5.99817 29.5372 7.88903L29.5324 18.5297C29.5324 19.1763 28.9888 19.6976 28.3178 19.6976H26.6365C25.9655 19.6976 25.4219 19.1739 25.4219 18.5274V7.88671C25.4219 3.80607 28.8566 0.49707 33.0922 0.49707C37.3278 0.49707 40.7625 3.80607 40.7625 7.88671' fill='%233B68FC'/%3E%3Cpath d='M79.6506 0.845703H77.8299C77.159 0.845703 76.6152 1.36962 76.6152 2.0159V18.5262C76.6152 19.1724 77.159 19.6964 77.8299 19.6964H79.6506C80.3215 19.6964 80.8653 19.1724 80.8653 18.5262V2.0159C80.8653 1.36962 80.3215 0.845703 79.6506 0.845703Z' fill='%233B68FC'/%3E%3Cpath d='M8.06573 17.6786C7.70976 18.3251 6.84147 18.5314 6.22573 18.1027C5.24199 17.4168 4.38332 16.5617 3.70023 15.5862C3.28893 14.9976 2.94498 14.3743 2.67319 13.7231C2.38937 13.0465 2.81029 12.2841 3.54629 12.108L4.95336 11.7697C5.55707 11.6237 6.17041 11.9296 6.42055 12.4788C6.57449 12.8194 6.76209 13.1461 6.98097 13.459C7.37784 14.0267 7.85648 14.5179 8.40728 14.9211C8.89554 15.2803 9.02783 15.9361 8.7392 16.4598L8.06573 17.681V17.6786Z' fill='%23FFCC00'/%3E%3Cpath d='M12.3014 20.0001C11.9358 20.0001 11.5678 19.9816 11.2022 19.9445C10.4494 19.8681 9.94666 19.152 10.1295 18.4453L10.4782 17.0897C10.6273 16.5127 11.1878 16.1257 11.8035 16.1257H12.3014C12.9171 16.1257 13.4776 16.5127 13.6267 17.0897L13.9754 18.4453C14.1582 19.152 13.6555 19.8681 12.9027 19.9445C12.5371 19.9816 12.1691 20.0001 12.3014 20.0001Z' fill='%23FF4059'/%3E%3C/g%3E%3C/svg%3E">
                            </div>
                        </div>
                        <div class="footer-social-links">
                            <a href="https://facebook.com/ycalculatorcity" class="social-link" aria-label="Facebook"><svg viewBox="0 0 36 36"><path d="M31.4975 18.0824C31.4975 10.5807 25.4536 4.49976 17.9992 4.49976C10.5414 4.50144 4.49756 10.5807 4.49756 18.0841C4.49756 24.8619 9.43455 30.4805 15.8867 31.4996V22.0087H12.4615V18.0841H15.8901V15.0891C15.8901 11.6859 17.9064 9.80626 20.9891 9.80626C22.4671 9.80626 24.011 10.0712 24.011 10.0712V13.412H22.3085C20.6331 13.412 20.11 14.4598 20.11 15.5346V18.0824H23.8524L23.2551 22.007H20.1083V31.498C26.5605 30.4788 31.4975 24.8602 31.4975 18.0824Z"></path></svg></a>
                            <a href="https://twitter.com/ycalculatorcity" class="social-link" aria-label="Twitter"><svg viewBox="0 0 36 36"><path d="M32.2 9.3c-1.2.5-2.4.9-3.8 1 1.4-.8 2.4-2.1 2.9-3.6-1.3.8-2.7 1.3-4.2 1.6C25.9 6.8 24.3 6 22.5 6c-3.6 0-6.6 2.9-6.6 6.6 0 .5.1 1 .2 1.5-5.5-.3-10.3-2.9-13.6-6.9-.6 1-.9 2.1-.9 3.3 0 2.3 1.2 4.3 2.9 5.5-1.1 0-2.1-.3-3-.8v.1c0 3.2 2.3 5.8 5.3 6.4-.6.1-1.1.2-1.7.2-.4 0-.8 0-1.2-.1.8 2.6 3.3 4.5 6.1 4.6-2.2 1.8-5.1 2.8-8.2 2.8-.5 0-1.1 0-1.6-.1 2.9 1.9 6.4 3 10.1 3 12.1 0 18.7-10 18.7-18.7v-.8c1.3-1 2.4-2.2 3.3-3.5z"></path></svg></a>
                            <a href="https://instagram.com/ycalculatorcity" class="social-link" aria-label="Instagram"><svg viewBox="0 0 36 36"><path d="M18 2.16c4.804 0 5.376.018 7.272.105 1.756.08 2.708.372 3.344.618.84.327 1.44.717 2.07 1.347.63.63 1.02 1.23 1.347 2.07.246.636.538 1.588.618 3.344.087 1.896.105 2.468.105 7.272s-.018 5.376-.105 7.272c-.08 1.756-.372 2.708-.618 3.344-.327.84-.717 1.44-1.347 2.07-.63.63-1.23 1.02-2.07 1.347-.636.246-1.588.538-3.344.618-1.896.087-2.468.105-7.272.105s-5.376-.018-7.272-.105c-1.756-.08-2.708-.372-3.344-.618-.84-.327-1.44-.717-2.07-1.347-.63-.63-1.02-1.23-1.347-2.07-.246-.636-.538-1.588-.618-3.344C2.178 23.376 2.16 22.804 2.16 18s.018-5.376.105-7.272c.08-1.756.372-2.708.618-3.344.327-.84.717-1.44 1.347-2.07.63-.63 1.23-1.02 2.07-1.347.636-.246 1.588-.538 3.344-.618C12.624 2.178 13.196 2.16 18 2.16M18 0C13.108 0 12.492.021 10.578.108 8.67.195 7.368.504 6.24.954c-1.176.456-2.172 1.068-3.168 2.064C2.076 4.014 1.464 5.01 1.008 6.186.558 7.314.249 8.616.162 10.524.075 12.438.054 13.054.054 17.946s.021 5.508.108 7.422c.087 1.908.396 3.21.846 4.338.456 1.176 1.068 2.172 2.064 3.168.996.996 1.992 1.608 3.168 2.064 1.128.45 2.43.759 4.338.846 1.914.087 2.53.108 7.422.108s5.508-.021 7.422-.108c1.908-.087 3.21-.396 4.338-.846 1.176-.456 2.172-1.068 3.168-2.064.996-.996 1.608-1.992 2.064-3.168.45-1.128.759-2.43.846-4.338.087-1.914.108-2.53.108-7.422s-.021-5.508-.108-7.422c-.087-1.908-.396-3.21-.846-4.338-.456-1.176-1.068-2.172-2.064-3.168C28.986 2.076 27.99 1.464 26.814 1.008 25.686.558 24.384.249 22.476.162 20.562.075 19.946.054 18.054.054L18 0z"></path><path d="M18 8.784c-5.088 0-9.216 4.128-9.216 9.216s4.128 9.216 9.216 9.216 9.216-4.128 9.216-9.216S23.088 8.784 18 8.784zM18 24c-3.312 0-6-2.688-6-6s2.688-6 6-6 6 2.688 6 6-2.688 6-6 6z"></path><circle cx="27.216" cy="8.784" r="2.16"></circle></svg></a>
                            <a href="https://linkedin.com/company/ycalculatorcity" class="social-link" aria-label="LinkedIn"><svg viewBox="0 0 36 36"><path d="M8.64 13.68h4.32v13.68H8.64V13.68zM10.8 5.04c1.38 0 2.52 1.14 2.52 2.52s-1.14 2.52-2.52 2.52-2.52-1.14-2.52-2.52S9.42 5.04 10.8 5.04zM15.12 13.68h4.14v1.86h.06c.576-.96 1.98-1.98 4.08-1.98 4.368 0 5.178 2.88 5.178 6.624v7.68h-4.32v-6.804c0-1.62-.03-3.696-2.25-3.696-2.25 0-2.592 1.758-2.592 3.576v6.924h-4.32V13.68z"></path></svg></a>
                        </div>
                    </div>
                    <div class="footer-links-section">
                        <div class="footer-category-section">
                            <span class="footer-category-title">Popular Calculators</span>
                            <div class="footer-category-list">
                                <a href="/biological-age-calculator" class="nav-link"><span class="nav-link-text">Biological Age Calculator</span></a>
                                <a href="/ap-biology-calculator" class="nav-link"><span class="nav-link-text">AP Biology Calculator</span></a>
                                <a href="/bmi-calculator" class="nav-link"><span class="nav-link-text">BMI Calculator</span></a>
                                <a href="/molarity-calculator" class="nav-link"><span class="nav-link-text">Molarity Calculator</span></a>
                                <a href="/loan-calculator" class="nav-link"><span class="nav-link-text">Loan Calculator</span></a>
                                <a href="/calorie-calculator" class="nav-link"><span class="nav-link-text">Calorie Calculator</span></a>
                            </div>
                        </div>
                        <div class="footer-category-section">
                            <span class="footer-category-title">Categories</span>
                            <div class="footer-category-list">
                                <a href="/biology-calculators" class="nav-link"><span class="nav-link-text">Biology</span></a>
                                <a href="/chemistry-calculators" class="nav-link"><span class="nav-link-text">Chemistry</span></a>
                                <a href="/math-calculators" class="nav-link"><span class="nav-link-text">Math</span></a>
                                <a href="/physics-calculators" class="nav-link"><span class="nav-link-text">Physics</span></a>
                                <a href="/health-calculators" class="nav-link"><span class="nav-link-text">Health</span></a>
                                <a href="/finance-calculators" class="nav-link"><span class="nav-link-text">Finance</span></a>
                            </div>
                        </div>
                        <div class="footer-category-section">
                            <span class="footer-category-title">Company</span>
                            <div class="footer-category-list" style="grid-template-columns: 1fr;">
                                <a href="/about" class="nav-link"><span class="nav-link-text">About Us</span></a>
                                <a href="/contact" class="nav-link"><span class="nav-link-text">Contact</span></a>
                                <a href="/privacy-policy" class="nav-link"><span class="nav-link-text">Privacy Policy</span></a>
                                <a href="/terms-of-service" class="nav-link"><span class="nav-link-text">Terms of Service</span></a>
                                <a href="/sitemap.xml" class="nav-link"><span class="nav-link-text">Sitemap</span></a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer-base">
                    <span class="footer-copyright">© 2024 y.calculator.city. All rights reserved.</span>
                    <div style="display: flex; gap: 16px;">
                        <a href="/privacy-policy" class="footer-base-link">Privacy Policy</a>
                        <a href="/terms-of-service" class="footer-base-link">Terms of Service</a>
                        <a href="/contact" class="footer-base-link">Contact</a>
                    </div>
                </div>
            </div>
        </footer>
    </div>
</body>
</html>
